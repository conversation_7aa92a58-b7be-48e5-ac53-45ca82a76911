#!/usr/bin/env python3
"""
解析WAV文件中的自定义chunk，特别是prof和trig chunk
这些chunk很可能包含GNSS信息
"""

import struct
import os
import datetime
from typing import Optional, Dict, Any, List

def read_wav_chunks(filename: str) -> Dict[str, bytes]:
    """读取WAV文件的所有chunk"""
    chunks = {}
    
    with open(filename, 'rb') as f:
        # 跳过RIFF头
        f.read(12)
        
        # 读取所有chunk
        while True:
            try:
                chunk_header = f.read(8)
                if len(chunk_header) < 8:
                    break
                
                chunk_id, chunk_size = struct.unpack('<4sI', chunk_header)
                chunk_id_str = chunk_id.decode('ascii', errors='ignore')
                
                chunk_data = f.read(chunk_size)
                chunks[chunk_id_str] = chunk_data
                
                # 如果chunk大小是奇数，跳过填充字节
                if chunk_size % 2 == 1:
                    f.read(1)
                    
            except Exception as e:
                break
                
    return chunks

def analyze_prof_chunk(data: bytes) -> Dict[str, Any]:
    """分析prof chunk - 可能包含配置文件信息"""
    print(f"\n=== 分析prof chunk ({len(data)} 字节) ===")
    
    result = {}
    
    # 尝试作为文本解析
    try:
        text = data.decode('utf-8', errors='ignore')
        print(f"作为UTF-8文本:")
        print(f"{text[:200]}...")
        
        # 查找GNSS相关关键词
        gnss_keywords = ['timestamp', 'latitude', 'longitude', 'GPS', 'time', 'lat', 'lon', 'position']
        found_keywords = []
        
        for keyword in gnss_keywords:
            if keyword.lower() in text.lower():
                found_keywords.append(keyword)
                pos = text.lower().find(keyword.lower())
                context = text[max(0, pos-30):pos+50]
                print(f"找到关键词 '{keyword}': {context}")
        
        result['text'] = text
        result['found_keywords'] = found_keywords
        
    except Exception as e:
        print(f"UTF-8解码失败: {e}")
    
    # 尝试作为ASCII解析
    try:
        ascii_text = data.decode('ascii', errors='ignore')
        if ascii_text != text:
            print(f"\n作为ASCII文本:")
            print(f"{ascii_text[:200]}...")
    except:
        pass
    
    # 分析二进制结构
    print(f"\n二进制数据分析:")
    print(f"前32字节 (hex): {data[:32].hex()}")
    print(f"前32字节 (ascii): {data[:32].decode('ascii', errors='replace')}")
    
    # 尝试解析为结构化数据
    if len(data) >= 8:
        try:
            # 尝试不同的数据格式
            formats = [
                ('<II', '两个32位整数'),
                ('<QQ', '两个64位整数'),
                ('<dd', '两个64位浮点数'),
                ('<ff', '两个32位浮点数'),
            ]
            
            for fmt, desc in formats:
                try:
                    values = struct.unpack(fmt, data[:struct.calcsize(fmt)])
                    print(f"{desc}: {values}")
                except:
                    pass
        except:
            pass
    
    return result

def analyze_trig_chunk(data: bytes) -> Dict[str, Any]:
    """分析trig chunk - 可能包含触发/时间戳信息"""
    print(f"\n=== 分析trig chunk ({len(data)} 字节) ===")
    
    result = {}
    
    # 这个chunk很大，可能包含时间序列数据
    print(f"chunk大小: {len(data):,} 字节")
    
    # 分析头部
    if len(data) >= 64:
        print(f"头部64字节 (hex): {data[:64].hex()}")
        print(f"头部64字节 (ascii): {data[:64].decode('ascii', errors='replace')}")
        
        # 尝试解析头部结构
        try:
            # 假设头部包含一些元数据
            header_formats = [
                ('<16I', '16个32位整数'),
                ('<8Q', '8个64位整数'),
                ('<8d', '8个64位浮点数'),
            ]
            
            for fmt, desc in header_formats:
                try:
                    values = struct.unpack(fmt, data[:struct.calcsize(fmt)])
                    print(f"头部作为{desc}: {values[:4]}... (显示前4个)")
                    
                    # 检查是否有合理的时间戳值
                    for i, val in enumerate(values):
                        if isinstance(val, (int, float)):
                            # 检查是否是Unix时间戳 (1970年以后)
                            if 1000000000 < val < 2000000000:  # 2001-2033年范围
                                dt = datetime.datetime.fromtimestamp(val)
                                print(f"  位置{i}可能是时间戳: {val} -> {dt}")
                            # 检查是否是GPS时间戳 (1980年以后)
                            elif 315964800 < val < 1893456000:  # GPS时间范围
                                gps_epoch = datetime.datetime(1980, 1, 6)
                                dt = gps_epoch + datetime.timedelta(seconds=val)
                                print(f"  位置{i}可能是GPS时间: {val} -> {dt}")
                            # 检查是否是纬度/经度
                            elif -180 <= val <= 180:
                                print(f"  位置{i}可能是坐标: {val}")
                except Exception as e:
                    pass
        except:
            pass
    
    # 分析数据模式
    if len(data) > 1000:
        # 检查是否有重复的数据结构
        record_sizes = [4, 8, 12, 16, 20, 24, 32, 64]
        
        for record_size in record_sizes:
            if len(data) % record_size == 0:
                num_records = len(data) // record_size
                print(f"如果每条记录{record_size}字节，共有{num_records}条记录")
                
                if num_records < 1000000:  # 合理的记录数量
                    # 分析前几条记录
                    print(f"  前3条记录:")
                    for i in range(min(3, num_records)):
                        record_data = data[i*record_size:(i+1)*record_size]
                        print(f"    记录{i}: {record_data.hex()}")
                        
                        # 尝试解析为不同格式
                        if record_size == 8:
                            try:
                                val1, val2 = struct.unpack('<II', record_data)
                                print(f"      作为两个uint32: {val1}, {val2}")
                            except:
                                pass
                            try:
                                val = struct.unpack('<Q', record_data)[0]
                                print(f"      作为uint64: {val}")
                            except:
                                pass
                            try:
                                val = struct.unpack('<d', record_data)[0]
                                print(f"      作为double: {val}")
                            except:
                                pass
                        elif record_size == 12:
                            try:
                                val1, val2, val3 = struct.unpack('<III', record_data)
                                print(f"      作为三个uint32: {val1}, {val2}, {val3}")
                                # 检查是否是时间戳+坐标
                                if 1000000000 < val1 < 2000000000:
                                    dt = datetime.datetime.fromtimestamp(val1)
                                    print(f"        可能格式: 时间戳={dt}, 坐标=({val2}, {val3})")
                            except:
                                pass
                        elif record_size == 24:
                            try:
                                val1, val2, val3 = struct.unpack('<ddd', record_data)
                                print(f"      作为三个double: {val1}, {val2}, {val3}")
                                # 检查是否是时间戳+纬度+经度
                                if 1000000000 < val1 < 2000000000:
                                    dt = datetime.datetime.fromtimestamp(val1)
                                    if -90 <= val2 <= 90 and -180 <= val3 <= 180:
                                        print(f"        可能格式: 时间戳={dt}, 纬度={val2}, 经度={val3}")
                            except:
                                pass
    
    return result

def search_gnss_patterns(data: bytes) -> List[Dict[str, Any]]:
    """在数据中搜索GNSS模式"""
    patterns = []
    
    # 搜索可能的时间戳模式
    for i in range(0, len(data) - 8, 4):
        try:
            # 尝试读取32位时间戳
            timestamp = struct.unpack('<I', data[i:i+4])[0]
            if 1000000000 < timestamp < 2000000000:  # 合理的Unix时间戳
                dt = datetime.datetime.fromtimestamp(timestamp)
                
                # 检查后续是否有坐标数据
                if i + 16 <= len(data):
                    try:
                        lat = struct.unpack('<f', data[i+4:i+8])[0]
                        lon = struct.unpack('<f', data[i+8:i+12])[0]
                        
                        if -90 <= lat <= 90 and -180 <= lon <= 180:
                            patterns.append({
                                'offset': i,
                                'timestamp': timestamp,
                                'datetime': dt,
                                'latitude': lat,
                                'longitude': lon,
                                'format': 'uint32_timestamp + float_lat + float_lon'
                            })
                    except:
                        pass
            
            # 尝试读取64位时间戳
            if i + 8 <= len(data):
                timestamp64 = struct.unpack('<Q', data[i:i+8])[0]
                if 1000000000 < timestamp64 < 2000000000:
                    dt = datetime.datetime.fromtimestamp(timestamp64)
                    
                    if i + 24 <= len(data):
                        try:
                            lat = struct.unpack('<d', data[i+8:i+16])[0]
                            lon = struct.unpack('<d', data[i+16:i+24])[0]
                            
                            if -90 <= lat <= 90 and -180 <= lon <= 180:
                                patterns.append({
                                    'offset': i,
                                    'timestamp': timestamp64,
                                    'datetime': dt,
                                    'latitude': lat,
                                    'longitude': lon,
                                    'format': 'uint64_timestamp + double_lat + double_lon'
                                })
                        except:
                            pass
        except:
            pass
    
    return patterns

def main():
    filename = "0053_20250723_202435.part1.iq.wav"
    
    if not os.path.exists(filename):
        print(f"错误: 文件 {filename} 不存在")
        return
    
    print(f"解析WAV文件中的自定义chunk: {filename}")
    print("=" * 60)
    
    # 读取所有chunk
    chunks = read_wav_chunks(filename)
    
    # 分析prof chunk
    if 'prof' in chunks:
        prof_result = analyze_prof_chunk(chunks['prof'])
    
    # 分析trig chunk
    if 'trig' in chunks:
        trig_result = analyze_trig_chunk(chunks['trig'])
        
        # 在trig chunk中搜索GNSS模式
        print(f"\n搜索GNSS数据模式...")
        patterns = search_gnss_patterns(chunks['trig'])
        
        if patterns:
            print(f"找到 {len(patterns)} 个可能的GNSS数据模式:")
            for i, pattern in enumerate(patterns[:5]):  # 只显示前5个
                print(f"  模式 {i+1}:")
                print(f"    偏移: {pattern['offset']}")
                print(f"    时间戳: {pattern['timestamp']}")
                print(f"    日期时间: {pattern['datetime']}")
                print(f"    纬度: {pattern['latitude']}")
                print(f"    经度: {pattern['longitude']}")
                print(f"    格式: {pattern['format']}")
        else:
            print(f"未找到明显的GNSS数据模式")
    
    print(f"\n分析完成")

if __name__ == "__main__":
    main()
