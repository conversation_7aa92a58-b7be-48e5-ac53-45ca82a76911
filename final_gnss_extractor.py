#!/usr/bin/env python3
"""
最终的GNSS信息提取器
基于对trig chunk的深入分析，提取AbsoluteTimeStamp、Latitude、Longitude
"""

import struct
import os
import datetime
from typing import List, Dict, Any, Tuple

def read_trig_chunk(filename: str) -> bytes:
    """读取trig chunk数据"""
    with open(filename, 'rb') as f:
        # 跳过RIFF头
        f.read(12)
        
        # 查找trig chunk
        while True:
            try:
                chunk_header = f.read(8)
                if len(chunk_header) < 8:
                    break
                
                chunk_id, chunk_size = struct.unpack('<4sI', chunk_header)
                chunk_id_str = chunk_id.decode('ascii', errors='ignore')
                
                if chunk_id_str == 'trig':
                    return f.read(chunk_size)
                else:
                    # 跳过这个chunk
                    f.seek(chunk_size, 1)
                    if chunk_size % 2 == 1:
                        f.read(1)  # 填充字节
                        
            except Exception as e:
                break
                
    return b''

def extract_gnss_records(data: bytes) -> List[Dict[str, Any]]:
    """提取GNSS记录"""
    records = []
    
    print(f"分析trig chunk数据 ({len(data)} 字节)...")
    
    # 基于分析结果，尝试28字节记录格式，头部512字节
    header_size = 512
    record_size = 28
    
    if len(data) <= header_size:
        print("数据太小，无法包含记录")
        return records
    
    data_section = data[header_size:]
    
    if len(data_section) % record_size != 0:
        print(f"数据长度 {len(data_section)} 不是记录大小 {record_size} 的倍数")
        # 尝试其他可能的头部大小
        for alt_header in [0, 256, 1024]:
            alt_data = data[alt_header:]
            if len(alt_data) % record_size == 0:
                print(f"尝试头部大小 {alt_header}...")
                data_section = alt_data
                header_size = alt_header
                break
        else:
            print("无法找到合适的记录格式")
            return records
    
    num_records = len(data_section) // record_size
    print(f"假设头部 {header_size} 字节，记录大小 {record_size} 字节")
    print(f"可能有 {num_records} 条记录")
    
    # 分析前1000条记录寻找有效的GNSS数据
    valid_records = []
    
    for i in range(min(1000, num_records)):
        offset = header_size + i * record_size
        record_data = data[offset:offset + record_size]
        
        try:
            # 解析为7个uint32
            values = struct.unpack('<7I', record_data)
            
            # 检查第一个值是否是合理的时间戳
            timestamp = values[0]
            
            if 946684800 < timestamp < 1893456000:  # 2000-2030年
                dt = datetime.datetime.fromtimestamp(timestamp)
                
                # 检查后续值是否可能是坐标
                for j in range(1, len(values) - 1):
                    lat_raw = values[j]
                    lon_raw = values[j + 1]
                    
                    # 尝试不同的缩放因子
                    for scale in [1e-6, 1e-7, 1e-8]:
                        lat = lat_raw * scale
                        lon = lon_raw * scale
                        
                        # 检查是否在合理的地理坐标范围内
                        if -90 <= lat <= 90 and -180 <= lon <= 180:
                            record = {
                                'record_index': i,
                                'offset': offset,
                                'timestamp': timestamp,
                                'datetime': dt,
                                'latitude': lat,
                                'longitude': lon,
                                'scale_factor': scale,
                                'raw_values': values,
                                'lat_position': j,
                                'lon_position': j + 1
                            }
                            valid_records.append(record)
                            break
                    
                    if valid_records and valid_records[-1]['record_index'] == i:
                        break  # 已经找到这条记录的有效解释
        
        except Exception as e:
            continue
    
    print(f"找到 {len(valid_records)} 条有效的GNSS记录")
    
    # 如果找到了有效记录，继续处理更多记录
    if valid_records:
        # 使用第一条有效记录的格式来处理更多数据
        template = valid_records[0]
        scale = template['scale_factor']
        lat_pos = template['lat_position']
        lon_pos = template['lon_position']
        
        print(f"使用模板: 缩放因子={scale}, 纬度位置={lat_pos}, 经度位置={lon_pos}")
        
        # 处理更多记录（每100条取1条以提高速度）
        for i in range(0, num_records, 100):
            if i < 1000:  # 前1000条已经处理过
                continue
                
            offset = header_size + i * record_size
            record_data = data[offset:offset + record_size]
            
            try:
                values = struct.unpack('<7I', record_data)
                timestamp = values[0]
                
                if 946684800 < timestamp < 1893456000:
                    dt = datetime.datetime.fromtimestamp(timestamp)
                    
                    lat_raw = values[lat_pos]
                    lon_raw = values[lon_pos]
                    
                    lat = lat_raw * scale
                    lon = lon_raw * scale
                    
                    if -90 <= lat <= 90 and -180 <= lon <= 180:
                        record = {
                            'record_index': i,
                            'offset': offset,
                            'timestamp': timestamp,
                            'datetime': dt,
                            'latitude': lat,
                            'longitude': lon,
                            'scale_factor': scale,
                            'raw_values': values,
                            'lat_position': lat_pos,
                            'lon_position': lon_pos
                        }
                        valid_records.append(record)
            
            except Exception as e:
                continue
    
    # 按时间戳排序
    valid_records.sort(key=lambda x: x['timestamp'])
    
    return valid_records

def analyze_prof_chunk(filename: str) -> Dict[str, Any]:
    """分析prof chunk中的配置信息"""
    prof_info = {}
    
    with open(filename, 'rb') as f:
        # 跳过RIFF头
        f.read(12)
        
        # 查找prof chunk
        while True:
            try:
                chunk_header = f.read(8)
                if len(chunk_header) < 8:
                    break
                
                chunk_id, chunk_size = struct.unpack('<4sI', chunk_header)
                chunk_id_str = chunk_id.decode('ascii', errors='ignore')
                
                if chunk_id_str == 'prof':
                    prof_data = f.read(chunk_size)
                    
                    # 尝试解析为文本
                    try:
                        text = prof_data.decode('utf-8', errors='ignore')
                        prof_info['text'] = text
                        
                        # 查找可能的配置信息
                        if 'lat' in text.lower() or 'lon' in text.lower():
                            prof_info['contains_coordinates'] = True
                        
                    except:
                        pass
                    
                    # 分析二进制数据
                    prof_info['size'] = chunk_size
                    prof_info['hex_preview'] = prof_data[:64].hex()
                    
                    return prof_info
                else:
                    # 跳过这个chunk
                    f.seek(chunk_size, 1)
                    if chunk_size % 2 == 1:
                        f.read(1)  # 填充字节
                        
            except Exception as e:
                break
    
    return prof_info

def main():
    filename = "0053_20250723_202435.part1.iq.wav"
    
    if not os.path.exists(filename):
        print(f"错误: 文件 {filename} 不存在")
        return
    
    print(f"从WAV文件提取GNSS信息: {filename}")
    print("=" * 60)
    
    # 分析prof chunk
    prof_info = analyze_prof_chunk(filename)
    if prof_info:
        print(f"prof chunk信息:")
        for key, value in prof_info.items():
            if key == 'text' and len(str(value)) > 100:
                print(f"  {key}: {str(value)[:100]}...")
            else:
                print(f"  {key}: {value}")
    
    # 读取并分析trig chunk
    trig_data = read_trig_chunk(filename)
    
    if not trig_data:
        print("未找到trig chunk")
        return
    
    # 提取GNSS记录
    gnss_records = extract_gnss_records(trig_data)
    
    if not gnss_records:
        print("未找到有效的GNSS记录")
        return
    
    print(f"\n" + "="*60)
    print(f"GNSS信息提取结果:")
    print(f"总共找到 {len(gnss_records)} 条GNSS记录")
    
    # 显示统计信息
    if gnss_records:
        first_record = gnss_records[0]
        last_record = gnss_records[-1]
        
        print(f"\n时间范围:")
        print(f"  开始时间: {first_record['datetime']}")
        print(f"  结束时间: {last_record['datetime']}")
        
        duration = last_record['timestamp'] - first_record['timestamp']
        print(f"  持续时间: {duration} 秒 ({duration/3600:.2f} 小时)")
        
        # 坐标范围
        lats = [r['latitude'] for r in gnss_records]
        lons = [r['longitude'] for r in gnss_records]
        
        print(f"\n坐标范围:")
        print(f"  纬度: {min(lats):.8f} 到 {max(lats):.8f}")
        print(f"  经度: {min(lons):.8f} 到 {max(lons):.8f}")
        
        # 显示前几条记录
        print(f"\n前5条记录:")
        for i, record in enumerate(gnss_records[:5]):
            print(f"  记录 {i+1}:")
            print(f"    AbsoluteTimeStamp: {record['timestamp']}")
            print(f"    DateTime: {record['datetime']}")
            print(f"    Latitude: {record['latitude']:.8f}")
            print(f"    Longitude: {record['longitude']:.8f}")
        
        # 保存到文件
        output_file = 'extracted_gnss_data.txt'
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("WAV文件GNSS信息提取结果\n")
            f.write("="*50 + "\n\n")
            f.write(f"源文件: {filename}\n")
            f.write(f"提取时间: {datetime.datetime.now()}\n")
            f.write(f"总记录数: {len(gnss_records)}\n\n")
            
            f.write("详细记录:\n")
            f.write("-"*50 + "\n")
            
            for i, record in enumerate(gnss_records):
                f.write(f"记录 {i+1}:\n")
                f.write(f"  AbsoluteTimeStamp: {record['timestamp']}\n")
                f.write(f"  DateTime: {record['datetime']}\n")
                f.write(f"  Latitude: {record['latitude']:.8f}\n")
                f.write(f"  Longitude: {record['longitude']:.8f}\n")
                f.write(f"  ScaleFactor: {record['scale_factor']}\n")
                f.write(f"  RecordOffset: {record['offset']}\n")
                f.write("\n")
        
        print(f"\n详细数据已保存到: {output_file}")
        
        # 生成CSV格式
        csv_file = 'extracted_gnss_data.csv'
        with open(csv_file, 'w', encoding='utf-8') as f:
            f.write("Timestamp,DateTime,Latitude,Longitude\n")
            for record in gnss_records:
                f.write(f"{record['timestamp']},{record['datetime']},{record['latitude']:.8f},{record['longitude']:.8f}\n")
        
        print(f"CSV格式数据已保存到: {csv_file}")

if __name__ == "__main__":
    main()
