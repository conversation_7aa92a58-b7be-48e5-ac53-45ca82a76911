#!/usr/bin/env python3
"""
最终的GNSS信息提取器
基于对trig chunk的深入分析，提取AbsoluteTimeStamp、Latitude、Longitude
"""

import struct
import os
import datetime
from typing import List, Dict, Any, Tuple

def read_trig_chunk(filename: str) -> bytes:
    """读取trig chunk数据"""
    with open(filename, 'rb') as f:
        # 跳过RIFF头
        f.read(12)
        
        # 查找trig chunk
        while True:
            try:
                chunk_header = f.read(8)
                if len(chunk_header) < 8:
                    break
                
                chunk_id, chunk_size = struct.unpack('<4sI', chunk_header)
                chunk_id_str = chunk_id.decode('ascii', errors='ignore')
                
                if chunk_id_str == 'trig':
                    return f.read(chunk_size)
                else:
                    # 跳过这个chunk
                    f.seek(chunk_size, 1)
                    if chunk_size % 2 == 1:
                        f.read(1)  # 填充字节
                        
            except Exception as e:
                break
                
    return b''

def extract_gnss_records(data: bytes) -> List[Dict[str, Any]]:
    """提取GNSS记录 - 使用正确的格式: double(8字节) + float(4字节) + float(4字节)"""
    records = []

    print(f"分析trig chunk数据 ({len(data)} 字节)...")
    print("使用正确的GNSS格式: AbsoluteTimeStamp(double,8字节) + Latitude(float,4字节) + Longitude(float,4字节)")

    # GNSS记录格式: 8字节double时间戳 + 4字节float纬度 + 4字节float经度 = 16字节
    record_size = 16

    # 尝试不同的头部大小
    possible_headers = [0, 64, 128, 256, 512, 1024]

    best_header_size = 0
    best_valid_count = 0

    for header_size in possible_headers:
        if len(data) <= header_size:
            continue

        data_section = data[header_size:]

        if len(data_section) < record_size:
            continue

        # 测试前100条记录
        test_records = min(100, len(data_section) // record_size)
        valid_count = 0

        for i in range(test_records):
            offset = i * record_size
            record_data = data_section[offset:offset + record_size]

            try:
                # 解析: double时间戳 + float纬度 + float经度
                timestamp, latitude, longitude = struct.unpack('<dff', record_data)

                # 验证时间戳 (合理的Unix时间戳范围)
                if 946684800 < timestamp < 1893456000:  # 2000-2030年
                    # 验证坐标范围
                    if -90 <= latitude <= 90 and -180 <= longitude <= 180:
                        valid_count += 1
            except:
                continue

        print(f"头部大小 {header_size}: 测试 {test_records} 条记录, 有效 {valid_count} 条")

        if valid_count > best_valid_count:
            best_valid_count = valid_count
            best_header_size = header_size

    if best_valid_count == 0:
        print("未找到有效的GNSS记录格式")
        return records

    print(f"选择最佳头部大小: {best_header_size} 字节")

    # 使用最佳头部大小提取所有记录
    data_section = data[best_header_size:]
    num_records = len(data_section) // record_size

    print(f"总共可能有 {num_records} 条GNSS记录")

    valid_records = []

    for i in range(num_records):
        offset = best_header_size + i * record_size
        record_data = data[offset:offset + record_size]

        try:
            # 解析: double时间戳 + float纬度 + float经度
            timestamp, latitude, longitude = struct.unpack('<dff', record_data)

            # 验证数据有效性
            if (946684800 < timestamp < 1893456000 and  # 合理的时间范围
                -90 <= latitude <= 90 and               # 有效纬度
                -180 <= longitude <= 180):              # 有效经度

                dt = datetime.datetime.fromtimestamp(timestamp)

                record = {
                    'record_index': i,
                    'offset': offset,
                    'timestamp': timestamp,
                    'datetime': dt,
                    'latitude': latitude,
                    'longitude': longitude,
                    'format': 'double_timestamp + float_lat + float_lon'
                }
                valid_records.append(record)

        except Exception as e:
            continue

    print(f"成功提取 {len(valid_records)} 条有效的GNSS记录")

    # 按时间戳排序
    valid_records.sort(key=lambda x: x['timestamp'])

    return valid_records

def analyze_prof_chunk(filename: str) -> Dict[str, Any]:
    """分析prof chunk中的配置信息"""
    prof_info = {}
    
    with open(filename, 'rb') as f:
        # 跳过RIFF头
        f.read(12)
        
        # 查找prof chunk
        while True:
            try:
                chunk_header = f.read(8)
                if len(chunk_header) < 8:
                    break
                
                chunk_id, chunk_size = struct.unpack('<4sI', chunk_header)
                chunk_id_str = chunk_id.decode('ascii', errors='ignore')
                
                if chunk_id_str == 'prof':
                    prof_data = f.read(chunk_size)
                    
                    # 尝试解析为文本
                    try:
                        text = prof_data.decode('utf-8', errors='ignore')
                        prof_info['text'] = text
                        
                        # 查找可能的配置信息
                        if 'lat' in text.lower() or 'lon' in text.lower():
                            prof_info['contains_coordinates'] = True
                        
                    except:
                        pass
                    
                    # 分析二进制数据
                    prof_info['size'] = chunk_size
                    prof_info['hex_preview'] = prof_data[:64].hex()
                    
                    return prof_info
                else:
                    # 跳过这个chunk
                    f.seek(chunk_size, 1)
                    if chunk_size % 2 == 1:
                        f.read(1)  # 填充字节
                        
            except Exception as e:
                break
    
    return prof_info

def main():
    filename = "0053_20250723_202435.part1.iq.wav"
    
    if not os.path.exists(filename):
        print(f"错误: 文件 {filename} 不存在")
        return
    
    print(f"从WAV文件提取GNSS信息: {filename}")
    print("=" * 60)
    
    # 分析prof chunk
    prof_info = analyze_prof_chunk(filename)
    if prof_info:
        print(f"prof chunk信息:")
        for key, value in prof_info.items():
            if key == 'text' and len(str(value)) > 100:
                print(f"  {key}: {str(value)[:100]}...")
            else:
                print(f"  {key}: {value}")
    
    # 读取并分析trig chunk
    trig_data = read_trig_chunk(filename)
    
    if not trig_data:
        print("未找到trig chunk")
        return
    
    # 提取GNSS记录
    gnss_records = extract_gnss_records(trig_data)
    
    if not gnss_records:
        print("未找到有效的GNSS记录")
        return
    
    print(f"\n" + "="*60)
    print(f"GNSS信息提取结果:")
    print(f"总共找到 {len(gnss_records)} 条GNSS记录")
    
    # 显示统计信息
    if gnss_records:
        first_record = gnss_records[0]
        last_record = gnss_records[-1]
        
        print(f"\n时间范围:")
        print(f"  开始时间: {first_record['datetime']}")
        print(f"  结束时间: {last_record['datetime']}")
        
        duration = last_record['timestamp'] - first_record['timestamp']
        print(f"  持续时间: {duration} 秒 ({duration/3600:.2f} 小时)")
        
        # 坐标范围
        lats = [r['latitude'] for r in gnss_records]
        lons = [r['longitude'] for r in gnss_records]
        
        print(f"\n坐标范围:")
        print(f"  纬度: {min(lats):.8f} 到 {max(lats):.8f}")
        print(f"  经度: {min(lons):.8f} 到 {max(lons):.8f}")
        
        # 显示前几条记录
        print(f"\n前5条记录:")
        for i, record in enumerate(gnss_records[:5]):
            print(f"  记录 {i+1}:")
            print(f"    AbsoluteTimeStamp: {record['timestamp']}")
            print(f"    DateTime: {record['datetime']}")
            print(f"    Latitude: {record['latitude']:.8f}")
            print(f"    Longitude: {record['longitude']:.8f}")
        
        # 保存到文件
        output_file = 'extracted_gnss_data.txt'
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("WAV文件GNSS信息提取结果\n")
            f.write("="*50 + "\n\n")
            f.write(f"源文件: {filename}\n")
            f.write(f"提取时间: {datetime.datetime.now()}\n")
            f.write(f"总记录数: {len(gnss_records)}\n\n")
            
            f.write("详细记录:\n")
            f.write("-"*50 + "\n")
            
            for i, record in enumerate(gnss_records):
                f.write(f"记录 {i+1}:\n")
                f.write(f"  AbsoluteTimeStamp: {record['timestamp']}\n")
                f.write(f"  DateTime: {record['datetime']}\n")
                f.write(f"  Latitude: {record['latitude']:.8f}\n")
                f.write(f"  Longitude: {record['longitude']:.8f}\n")
                f.write(f"  ScaleFactor: {record['scale_factor']}\n")
                f.write(f"  RecordOffset: {record['offset']}\n")
                f.write("\n")
        
        print(f"\n详细数据已保存到: {output_file}")
        
        # 生成CSV格式
        csv_file = 'extracted_gnss_data.csv'
        with open(csv_file, 'w', encoding='utf-8') as f:
            f.write("Timestamp,DateTime,Latitude,Longitude\n")
            for record in gnss_records:
                f.write(f"{record['timestamp']},{record['datetime']},{record['latitude']:.8f},{record['longitude']:.8f}\n")
        
        print(f"CSV格式数据已保存到: {csv_file}")

if __name__ == "__main__":
    main()
