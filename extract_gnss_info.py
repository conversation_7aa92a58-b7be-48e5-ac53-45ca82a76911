#!/usr/bin/env python3
"""
从WAV文件中提取GNSS信息 (AbsoluteTimeStamp, Latitude, Longitude)
基于对trig chunk的分析结果
"""

import struct
import os
import datetime
from typing import Optional, Dict, Any, List, Tuple

def read_wav_chunks(filename: str) -> Dict[str, bytes]:
    """读取WAV文件的所有chunk"""
    chunks = {}
    
    with open(filename, 'rb') as f:
        # 跳过RIFF头
        f.read(12)
        
        # 读取所有chunk
        while True:
            try:
                chunk_header = f.read(8)
                if len(chunk_header) < 8:
                    break
                
                chunk_id, chunk_size = struct.unpack('<4sI', chunk_header)
                chunk_id_str = chunk_id.decode('ascii', errors='ignore')
                
                chunk_data = f.read(chunk_size)
                chunks[chunk_id_str] = chunk_data
                
                # 如果chunk大小是奇数，跳过填充字节
                if chunk_size % 2 == 1:
                    f.read(1)
                    
            except Exception as e:
                break
                
    return chunks

def extract_gnss_from_trig(data: bytes) -> List[Dict[str, Any]]:
    """从trig chunk中提取GNSS信息"""
    gnss_records = []
    
    print(f"分析trig chunk ({len(data)} 字节) 寻找GNSS数据...")
    
    # 基于之前的分析，尝试不同的数据格式
    formats_to_try = [
        # (记录大小, 格式字符串, 描述)
        (12, '<III', 'timestamp(uint32) + lat(uint32) + lon(uint32)'),
        (16, '<IIII', 'timestamp(uint32) + lat(uint32) + lon(uint32) + alt(uint32)'),
        (20, '<Iffff', 'timestamp(uint32) + lat(float) + lon(float) + alt(float) + extra(float)'),
        (24, '<Iddd', 'timestamp(uint32) + lat(double) + lon(double) + alt(double)'),
        (16, '<Iff', 'timestamp(uint32) + lat(float) + lon(float) + padding'),
        (12, '<Iff', 'timestamp(uint32) + lat(float) + lon(float)'),
    ]
    
    for record_size, format_str, description in formats_to_try:
        print(f"\n尝试格式: {description}")
        
        if len(data) % record_size != 0:
            print(f"  数据长度不是{record_size}的倍数，跳过")
            continue
        
        num_records = len(data) // record_size
        print(f"  可能有 {num_records} 条记录")
        
        valid_records = []
        
        # 检查前100条记录
        for i in range(min(100, num_records)):
            try:
                offset = i * record_size
                record_data = data[offset:offset + record_size]
                values = struct.unpack(format_str, record_data)
                
                # 第一个值应该是时间戳
                timestamp = values[0]
                
                # 检查时间戳是否合理 (2000-2030年)
                if 946684800 < timestamp < 1893456000:  # 2000-2030年
                    dt = datetime.datetime.fromtimestamp(timestamp)
                    
                    # 检查坐标是否合理
                    if len(values) >= 3:
                        lat_raw = values[1]
                        lon_raw = values[2]
                        
                        # 尝试不同的坐标解释方式
                        coordinate_interpretations = []
                        
                        # 1. 直接作为浮点数
                        if isinstance(lat_raw, float) and isinstance(lon_raw, float):
                            if -90 <= lat_raw <= 90 and -180 <= lon_raw <= 180:
                                coordinate_interpretations.append(('direct_float', lat_raw, lon_raw))
                        
                        # 2. 作为整数，除以不同的因子
                        if isinstance(lat_raw, int) and isinstance(lon_raw, int):
                            for factor in [1e6, 1e7, 1e8, 1e9]:
                                lat_scaled = lat_raw / factor
                                lon_scaled = lon_raw / factor
                                if -90 <= lat_scaled <= 90 and -180 <= lon_scaled <= 180:
                                    coordinate_interpretations.append((f'scaled_by_{factor}', lat_scaled, lon_scaled))
                        
                        # 3. 重新解释字节为浮点数
                        try:
                            lat_bytes = struct.pack('<I', lat_raw) if isinstance(lat_raw, int) else struct.pack('<f', lat_raw)
                            lon_bytes = struct.pack('<I', lon_raw) if isinstance(lon_raw, int) else struct.pack('<f', lon_raw)
                            
                            lat_as_float = struct.unpack('<f', lat_bytes)[0]
                            lon_as_float = struct.unpack('<f', lon_bytes)[0]
                            
                            if -90 <= lat_as_float <= 90 and -180 <= lon_as_float <= 180:
                                coordinate_interpretations.append(('reinterpreted_float', lat_as_float, lon_as_float))
                        except:
                            pass
                        
                        # 如果找到有效的坐标解释
                        for interp_method, lat, lon in coordinate_interpretations:
                            record = {
                                'record_index': i,
                                'offset': offset,
                                'timestamp': timestamp,
                                'datetime': dt,
                                'latitude': lat,
                                'longitude': lon,
                                'format': description,
                                'interpretation': interp_method,
                                'raw_values': values
                            }
                            
                            if len(values) > 3:
                                record['altitude'] = values[3] if len(values) > 3 else None
                            
                            valid_records.append(record)
                            break  # 只取第一个有效解释
                
            except Exception as e:
                continue
        
        if valid_records:
            print(f"  找到 {len(valid_records)} 条有效记录")
            gnss_records.extend(valid_records)
            
            # 显示前几条记录
            for j, record in enumerate(valid_records[:3]):
                print(f"    记录 {j+1}:")
                print(f"      时间: {record['datetime']}")
                print(f"      纬度: {record['latitude']}")
                print(f"      经度: {record['longitude']}")
                if 'altitude' in record and record['altitude'] is not None:
                    print(f"      高度: {record['altitude']}")
                print(f"      解释方法: {record['interpretation']}")
        else:
            print(f"  未找到有效记录")
    
    return gnss_records

def extract_gnss_from_prof(data: bytes) -> Dict[str, Any]:
    """从prof chunk中提取GNSS信息"""
    print(f"\n分析prof chunk ({len(data)} 字节) 寻找GNSS配置信息...")
    
    result = {}
    
    # 尝试解析为文本配置
    try:
        text = data.decode('utf-8', errors='ignore')
        
        # 查找可能的配置参数
        config_patterns = [
            ('latitude', ['lat', 'latitude']),
            ('longitude', ['lon', 'longitude', 'lng']),
            ('timestamp', ['time', 'timestamp', 'date']),
            ('sample_rate', ['rate', 'freq', 'sample']),
        ]
        
        for param_name, keywords in config_patterns:
            for keyword in keywords:
                if keyword in text.lower():
                    # 尝试提取数值
                    import re
                    pattern = rf'{keyword}\s*[=:]\s*([-+]?\d*\.?\d+)'
                    match = re.search(pattern, text, re.IGNORECASE)
                    if match:
                        value = float(match.group(1))
                        result[param_name] = value
                        print(f"  找到配置: {param_name} = {value}")
    
    except Exception as e:
        print(f"  解析prof chunk文本时出错: {e}")
    
    # 尝试解析为二进制结构
    if len(data) >= 16:
        try:
            # 尝试不同的结构
            structures = [
                ('<dd', 'lat_lon_double'),
                ('<ff', 'lat_lon_float'),
                ('<II', 'lat_lon_int'),
                ('<Qd', 'timestamp_lat'),
                ('<Qdd', 'timestamp_lat_lon'),
            ]
            
            for fmt, desc in structures:
                try:
                    size = struct.calcsize(fmt)
                    if size <= len(data):
                        values = struct.unpack(fmt, data[:size])
                        
                        if desc == 'lat_lon_double' and len(values) == 2:
                            lat, lon = values
                            if -90 <= lat <= 90 and -180 <= lon <= 180:
                                result['latitude_binary'] = lat
                                result['longitude_binary'] = lon
                                print(f"  二进制解析 ({desc}): 纬度={lat}, 经度={lon}")
                        
                        elif desc == 'timestamp_lat_lon' and len(values) == 3:
                            ts, lat, lon = values
                            if 946684800 < ts < 1893456000 and -90 <= lat <= 90 and -180 <= lon <= 180:
                                dt = datetime.datetime.fromtimestamp(ts)
                                result['timestamp_binary'] = ts
                                result['datetime_binary'] = dt
                                result['latitude_binary'] = lat
                                result['longitude_binary'] = lon
                                print(f"  二进制解析 ({desc}): 时间={dt}, 纬度={lat}, 经度={lon}")
                
                except:
                    pass
        
        except Exception as e:
            print(f"  解析prof chunk二进制时出错: {e}")
    
    return result

def main():
    filename = "0053_20250723_202435.part1.iq.wav"
    
    if not os.path.exists(filename):
        print(f"错误: 文件 {filename} 不存在")
        return
    
    print(f"从WAV文件提取GNSS信息: {filename}")
    print("=" * 60)
    
    # 读取所有chunk
    chunks = read_wav_chunks(filename)
    
    all_gnss_data = {}
    
    # 从prof chunk提取信息
    if 'prof' in chunks:
        prof_gnss = extract_gnss_from_prof(chunks['prof'])
        if prof_gnss:
            all_gnss_data['prof'] = prof_gnss
    
    # 从trig chunk提取信息
    if 'trig' in chunks:
        trig_gnss = extract_gnss_from_trig(chunks['trig'])
        if trig_gnss:
            all_gnss_data['trig'] = trig_gnss
    
    # 总结结果
    print(f"\n" + "="*60)
    print(f"GNSS信息提取结果:")
    
    if 'prof' in all_gnss_data:
        print(f"\n从prof chunk提取的信息:")
        for key, value in all_gnss_data['prof'].items():
            print(f"  {key}: {value}")
    
    if 'trig' in all_gnss_data:
        print(f"\n从trig chunk提取的信息:")
        trig_data = all_gnss_data['trig']
        print(f"  总共找到 {len(trig_data)} 条GNSS记录")
        
        if trig_data:
            # 显示第一条和最后一条记录
            first_record = trig_data[0]
            last_record = trig_data[-1]
            
            print(f"\n  第一条记录:")
            print(f"    AbsoluteTimeStamp: {first_record['timestamp']}")
            print(f"    DateTime: {first_record['datetime']}")
            print(f"    Latitude: {first_record['latitude']}")
            print(f"    Longitude: {first_record['longitude']}")
            
            if len(trig_data) > 1:
                print(f"\n  最后一条记录:")
                print(f"    AbsoluteTimeStamp: {last_record['timestamp']}")
                print(f"    DateTime: {last_record['datetime']}")
                print(f"    Latitude: {last_record['latitude']}")
                print(f"    Longitude: {last_record['longitude']}")
            
            # 保存到文件
            with open('gnss_extracted_data.txt', 'w', encoding='utf-8') as f:
                f.write("GNSS数据提取结果\n")
                f.write("="*50 + "\n\n")
                
                for i, record in enumerate(trig_data):
                    f.write(f"记录 {i+1}:\n")
                    f.write(f"  AbsoluteTimeStamp: {record['timestamp']}\n")
                    f.write(f"  DateTime: {record['datetime']}\n")
                    f.write(f"  Latitude: {record['latitude']}\n")
                    f.write(f"  Longitude: {record['longitude']}\n")
                    if 'altitude' in record and record['altitude'] is not None:
                        f.write(f"  Altitude: {record['altitude']}\n")
                    f.write(f"  Format: {record['format']}\n")
                    f.write(f"  Interpretation: {record['interpretation']}\n")
                    f.write("\n")
            
            print(f"\n详细数据已保存到: gnss_extracted_data.txt")
    
    if not all_gnss_data:
        print(f"\n未找到GNSS信息")
        print(f"可能的原因:")
        print(f"1. GNSS数据使用了不同的存储格式")
        print(f"2. 数据可能经过了加密或压缩")
        print(f"3. 需要特定的解码密钥或算法")

if __name__ == "__main__":
    main()
