#!/usr/bin/env python3
"""
深入分析trig chunk的数据结构
"""

import struct
import os
import datetime
from typing import Optional, Dict, Any, List, Tuple

def read_trig_chunk(filename: str) -> bytes:
    """读取trig chunk数据"""
    with open(filename, 'rb') as f:
        # 跳过RIFF头
        f.read(12)
        
        # 查找trig chunk
        while True:
            try:
                chunk_header = f.read(8)
                if len(chunk_header) < 8:
                    break
                
                chunk_id, chunk_size = struct.unpack('<4sI', chunk_header)
                chunk_id_str = chunk_id.decode('ascii', errors='ignore')
                
                if chunk_id_str == 'trig':
                    return f.read(chunk_size)
                else:
                    # 跳过这个chunk
                    f.seek(chunk_size, 1)
                    if chunk_size % 2 == 1:
                        f.read(1)  # 填充字节
                        
            except Exception as e:
                break
                
    return b''

def analyze_data_structure(data: bytes):
    """分析数据结构"""
    print(f"trig chunk总大小: {len(data):,} 字节")
    
    # 分析可能的头部大小
    possible_header_sizes = [0, 16, 32, 64, 128, 256, 512, 1024]
    
    for header_size in possible_header_sizes:
        if header_size >= len(data):
            continue
            
        remaining_data = len(data) - header_size
        print(f"\n假设头部大小为 {header_size} 字节:")
        print(f"  剩余数据: {remaining_data:,} 字节")
        
        # 检查剩余数据是否能被常见记录大小整除
        record_sizes = [4, 8, 12, 16, 20, 24, 28, 32, 64]
        
        for record_size in record_sizes:
            if remaining_data % record_size == 0:
                num_records = remaining_data // record_size
                print(f"    如果记录大小为 {record_size} 字节: {num_records:,} 条记录")
                
                # 如果记录数量合理，分析一下数据
                if 100 < num_records < 10000000:
                    analyze_records(data, header_size, record_size, min(10, num_records))

def analyze_records(data: bytes, header_size: int, record_size: int, num_to_analyze: int):
    """分析记录内容"""
    print(f"      分析前 {num_to_analyze} 条记录:")
    
    for i in range(num_to_analyze):
        offset = header_size + i * record_size
        record_data = data[offset:offset + record_size]
        
        print(f"        记录 {i+1} (偏移 {offset}):")
        print(f"          Hex: {record_data.hex()}")
        
        # 尝试不同的解释方式
        interpretations = []
        
        # 作为整数序列
        if record_size % 4 == 0:
            try:
                num_ints = record_size // 4
                ints = struct.unpack(f'<{num_ints}I', record_data)
                interpretations.append(f"uint32: {ints}")
                
                # 检查第一个是否是时间戳
                if 946684800 < ints[0] < 1893456000:  # 2000-2030年
                    dt = datetime.datetime.fromtimestamp(ints[0])
                    interpretations.append(f"第一个可能是时间戳: {dt}")
                
            except:
                pass
        
        # 作为浮点数序列
        if record_size % 4 == 0:
            try:
                num_floats = record_size // 4
                floats = struct.unpack(f'<{num_floats}f', record_data)
                interpretations.append(f"float: {floats}")
                
                # 检查是否有合理的坐标值
                for j, val in enumerate(floats):
                    if -90 <= val <= 90:
                        interpretations.append(f"位置{j}可能是纬度: {val}")
                    elif -180 <= val <= 180:
                        interpretations.append(f"位置{j}可能是经度: {val}")
            except:
                pass
        
        # 作为双精度浮点数
        if record_size % 8 == 0:
            try:
                num_doubles = record_size // 8
                doubles = struct.unpack(f'<{num_doubles}d', record_data)
                interpretations.append(f"double: {doubles}")
                
                # 检查是否有合理的坐标值
                for j, val in enumerate(doubles):
                    if -90 <= val <= 90:
                        interpretations.append(f"位置{j}可能是纬度: {val}")
                    elif -180 <= val <= 180:
                        interpretations.append(f"位置{j}可能是经度: {val}")
            except:
                pass
        
        # 混合格式：时间戳 + 坐标
        if record_size >= 12:
            try:
                # 时间戳(4字节) + 纬度(4字节) + 经度(4字节)
                timestamp, lat_raw, lon_raw = struct.unpack('<III', record_data[:12])
                if 946684800 < timestamp < 1893456000:
                    dt = datetime.datetime.fromtimestamp(timestamp)
                    
                    # 尝试不同的坐标缩放
                    for scale in [1, 1e-6, 1e-7, 1e-8]:
                        lat = lat_raw * scale if scale != 1 else struct.unpack('<f', struct.pack('<I', lat_raw))[0]
                        lon = lon_raw * scale if scale != 1 else struct.unpack('<f', struct.pack('<I', lon_raw))[0]
                        
                        if -90 <= lat <= 90 and -180 <= lon <= 180:
                            interpretations.append(f"时间戳+坐标(缩放{scale}): {dt}, 纬度={lat}, 经度={lon}")
            except:
                pass
        
        # 显示解释结果
        for interp in interpretations:
            print(f"          {interp}")

def search_timestamp_patterns(data: bytes):
    """搜索时间戳模式"""
    print(f"\n搜索时间戳模式...")
    
    found_timestamps = []
    
    # 每4字节检查一次
    for i in range(0, len(data) - 4, 4):
        try:
            timestamp = struct.unpack('<I', data[i:i+4])[0]
            
            # 检查是否是合理的时间戳
            if 946684800 < timestamp < 1893456000:  # 2000-2030年
                dt = datetime.datetime.fromtimestamp(timestamp)
                found_timestamps.append((i, timestamp, dt))
        except:
            pass
    
    print(f"找到 {len(found_timestamps)} 个可能的时间戳:")
    
    # 显示前10个
    for i, (offset, timestamp, dt) in enumerate(found_timestamps[:10]):
        print(f"  偏移 {offset}: {timestamp} -> {dt}")
        
        # 检查后续8字节是否是坐标
        if offset + 12 <= len(data):
            try:
                lat_raw = struct.unpack('<I', data[offset+4:offset+8])[0]
                lon_raw = struct.unpack('<I', data[offset+8:offset+12])[0]
                
                # 尝试作为浮点数
                lat_float = struct.unpack('<f', struct.pack('<I', lat_raw))[0]
                lon_float = struct.unpack('<f', struct.pack('<I', lon_raw))[0]
                
                if -90 <= lat_float <= 90 and -180 <= lon_float <= 180:
                    print(f"    后续可能是坐标: 纬度={lat_float}, 经度={lon_float}")
                
                # 尝试作为缩放整数
                for scale in [1e-6, 1e-7, 1e-8]:
                    lat_scaled = lat_raw * scale
                    lon_scaled = lon_raw * scale
                    
                    if -90 <= lat_scaled <= 90 and -180 <= lon_scaled <= 180:
                        print(f"    后续可能是缩放坐标(x{scale}): 纬度={lat_scaled}, 经度={lon_scaled}")
                        break
            except:
                pass

def analyze_file_header(data: bytes):
    """分析文件头部信息"""
    print(f"\n分析文件头部信息:")
    
    # 显示前256字节的详细信息
    header_size = min(256, len(data))
    header = data[:header_size]
    
    print(f"前{header_size}字节 (hex):")
    for i in range(0, header_size, 16):
        line = header[i:i+16]
        hex_str = ' '.join(f'{b:02x}' for b in line)
        ascii_str = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in line)
        print(f"  {i:04x}: {hex_str:<48} {ascii_str}")
    
    # 尝试解析为结构化头部
    print(f"\n尝试解析头部结构:")
    
    if len(data) >= 64:
        try:
            # 尝试不同的头部格式
            formats = [
                ('<16I', '16个uint32'),
                ('<8Q', '8个uint64'),
                ('<8d', '8个double'),
                ('<16f', '16个float'),
            ]
            
            for fmt, desc in formats:
                try:
                    values = struct.unpack(fmt, data[:struct.calcsize(fmt)])
                    print(f"  作为{desc}: {values[:4]}... (显示前4个)")
                    
                    # 检查是否有时间戳
                    for i, val in enumerate(values):
                        if isinstance(val, (int, float)) and 946684800 < val < 1893456000:
                            dt = datetime.datetime.fromtimestamp(val)
                            print(f"    位置{i}可能是时间戳: {val} -> {dt}")
                except:
                    pass
        except:
            pass

def main():
    filename = "0053_20250723_202435.part1.iq.wav"
    
    if not os.path.exists(filename):
        print(f"错误: 文件 {filename} 不存在")
        return
    
    print(f"深入分析trig chunk结构: {filename}")
    print("=" * 60)
    
    # 读取trig chunk
    trig_data = read_trig_chunk(filename)
    
    if not trig_data:
        print("未找到trig chunk")
        return
    
    # 分析文件头部
    analyze_file_header(trig_data)
    
    # 分析数据结构
    analyze_data_structure(trig_data)
    
    # 搜索时间戳模式
    search_timestamp_patterns(trig_data)

if __name__ == "__main__":
    main()
