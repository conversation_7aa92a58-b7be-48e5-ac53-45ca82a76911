#!/usr/bin/env python3
"""
正确的GNSS信息提取器
根据用户指定的格式从trig chunk中提取GNSS信息:
- AbsoluteTimeStamp: double (8字节)
- Latitude: float (4字节)  
- Longitude: float (4字节)
"""

import struct
import os
import datetime
from typing import List, Dict, Any

def read_trig_chunk(filename: str) -> bytes:
    """读取trig chunk数据"""
    with open(filename, 'rb') as f:
        # 跳过RIFF头
        f.read(12)
        
        # 查找trig chunk
        while True:
            try:
                chunk_header = f.read(8)
                if len(chunk_header) < 8:
                    break
                
                chunk_id, chunk_size = struct.unpack('<4sI', chunk_header)
                chunk_id_str = chunk_id.decode('ascii', errors='ignore')
                
                if chunk_id_str == 'trig':
                    return f.read(chunk_size)
                else:
                    # 跳过这个chunk
                    f.seek(chunk_size, 1)
                    if chunk_size % 2 == 1:
                        f.read(1)  # 填充字节
                        
            except Exception as e:
                break
                
    return b''

def extract_gnss_from_trig(data: bytes) -> List[Dict[str, Any]]:
    """
    从trig chunk中提取GNSS信息
    格式: AbsoluteTimeStamp(double,8字节) + Latitude(float,4字节) + Longitude(float,4字节)
    """
    records = []
    
    print(f"分析trig chunk数据 ({len(data):,} 字节)...")
    print("GNSS记录格式: AbsoluteTimeStamp(double,8字节) + Latitude(float,4字节) + Longitude(float,4字节)")
    
    # 每条GNSS记录: 8 + 4 + 4 = 16字节
    record_size = 16
    
    # 尝试不同的头部偏移量
    possible_offsets = [0, 64, 128, 256, 512, 1024, 2048]
    
    best_offset = 0
    best_valid_count = 0
    best_sample_records = []
    
    for offset in possible_offsets:
        if offset >= len(data):
            continue
            
        remaining_data = data[offset:]
        max_records = len(remaining_data) // record_size
        
        if max_records < 10:  # 至少需要10条记录才有意义
            continue
        
        print(f"\n测试偏移量 {offset}, 可能的记录数: {max_records:,}")
        
        valid_count = 0
        sample_records = []
        
        # 测试前1000条记录
        test_count = min(1000, max_records)
        
        for i in range(test_count):
            record_offset = offset + i * record_size
            record_data = data[record_offset:record_offset + record_size]
            
            try:
                # 解析为: double时间戳 + float纬度 + float经度
                timestamp, latitude, longitude = struct.unpack('<dff', record_data)
                
                # 验证时间戳 (Unix时间戳，2000-2030年)
                if 946684800 <= timestamp <= 1893456000:
                    # 验证坐标范围
                    if -90 <= latitude <= 90 and -180 <= longitude <= 180:
                        dt = datetime.datetime.fromtimestamp(timestamp)
                        
                        record = {
                            'index': i,
                            'offset': record_offset,
                            'timestamp': timestamp,
                            'datetime': dt,
                            'latitude': latitude,
                            'longitude': longitude
                        }
                        
                        valid_count += 1
                        if len(sample_records) < 5:  # 保存前5个样本
                            sample_records.append(record)
                
            except (struct.error, ValueError, OSError) as e:
                continue
        
        print(f"  有效记录: {valid_count}/{test_count} ({valid_count/test_count*100:.1f}%)")
        
        if sample_records:
            print(f"  样本记录:")
            for j, record in enumerate(sample_records):
                print(f"    {j+1}. {record['datetime']} - 纬度:{record['latitude']:.6f}, 经度:{record['longitude']:.6f}")
        
        if valid_count > best_valid_count:
            best_valid_count = valid_count
            best_offset = offset
            best_sample_records = sample_records.copy()
    
    if best_valid_count == 0:
        print("\n未找到有效的GNSS记录")
        return records
    
    print(f"\n选择最佳偏移量: {best_offset}, 有效记录率: {best_valid_count/min(1000, (len(data)-best_offset)//record_size)*100:.1f}%")
    
    # 使用最佳偏移量提取所有记录
    remaining_data = data[best_offset:]
    total_records = len(remaining_data) // record_size
    
    print(f"开始提取所有 {total_records:,} 条记录...")
    
    all_records = []
    
    for i in range(total_records):
        record_offset = best_offset + i * record_size
        record_data = data[record_offset:record_offset + record_size]
        
        try:
            timestamp, latitude, longitude = struct.unpack('<dff', record_data)
            
            # 验证数据有效性
            if (946684800 <= timestamp <= 1893456000 and
                -90 <= latitude <= 90 and
                -180 <= longitude <= 180):
                
                dt = datetime.datetime.fromtimestamp(timestamp)
                
                record = {
                    'index': i,
                    'offset': record_offset,
                    'timestamp': timestamp,
                    'datetime': dt,
                    'latitude': latitude,
                    'longitude': longitude
                }
                all_records.append(record)
        
        except (struct.error, ValueError, OSError):
            continue
    
    # 按时间戳排序
    all_records.sort(key=lambda x: x['timestamp'])
    
    print(f"成功提取 {len(all_records):,} 条有效GNSS记录")
    
    return all_records

def save_gnss_data(records: List[Dict[str, Any]], filename_prefix: str = "gnss_correct"):
    """保存GNSS数据到文件"""
    if not records:
        print("没有数据可保存")
        return
    
    # 保存为CSV格式
    csv_filename = f"{filename_prefix}.csv"
    with open(csv_filename, 'w', encoding='utf-8') as f:
        f.write("Index,Offset,AbsoluteTimeStamp,DateTime,Latitude,Longitude\n")
        for record in records:
            f.write(f"{record['index']},{record['offset']},{record['timestamp']:.6f},"
                   f"{record['datetime']},{record['latitude']:.8f},{record['longitude']:.8f}\n")
    
    print(f"CSV数据已保存到: {csv_filename}")
    
    # 保存为详细文本格式
    txt_filename = f"{filename_prefix}.txt"
    with open(txt_filename, 'w', encoding='utf-8') as f:
        f.write("GNSS信息提取结果 (正确格式)\n")
        f.write("="*60 + "\n\n")
        f.write(f"提取时间: {datetime.datetime.now()}\n")
        f.write(f"总记录数: {len(records):,}\n")
        f.write(f"数据格式: AbsoluteTimeStamp(double,8字节) + Latitude(float,4字节) + Longitude(float,4字节)\n\n")
        
        if records:
            first = records[0]
            last = records[-1]
            
            f.write("时间范围:\n")
            f.write(f"  开始: {first['datetime']} (时间戳: {first['timestamp']:.6f})\n")
            f.write(f"  结束: {last['datetime']} (时间戳: {last['timestamp']:.6f})\n")
            f.write(f"  持续: {last['timestamp'] - first['timestamp']:.0f} 秒\n\n")
            
            lats = [r['latitude'] for r in records]
            lons = [r['longitude'] for r in records]
            
            f.write("坐标范围:\n")
            f.write(f"  纬度: {min(lats):.8f} 到 {max(lats):.8f}\n")
            f.write(f"  经度: {min(lons):.8f} 到 {max(lons):.8f}\n\n")
        
        f.write("详细记录:\n")
        f.write("-"*60 + "\n")
        
        for i, record in enumerate(records):
            f.write(f"记录 {i+1:,}:\n")
            f.write(f"  索引: {record['index']}\n")
            f.write(f"  偏移: {record['offset']}\n")
            f.write(f"  AbsoluteTimeStamp: {record['timestamp']:.6f}\n")
            f.write(f"  DateTime: {record['datetime']}\n")
            f.write(f"  Latitude: {record['latitude']:.8f}\n")
            f.write(f"  Longitude: {record['longitude']:.8f}\n")
            f.write("\n")
    
    print(f"详细数据已保存到: {txt_filename}")

def main():
    filename = "0053_20250723_202435.part1.iq.wav"
    
    if not os.path.exists(filename):
        print(f"错误: 文件 {filename} 不存在")
        return
    
    print(f"从WAV文件提取GNSS信息: {filename}")
    print("使用正确的数据格式: AbsoluteTimeStamp(double) + Latitude(float) + Longitude(float)")
    print("="*80)
    
    # 读取trig chunk
    trig_data = read_trig_chunk(filename)
    
    if not trig_data:
        print("错误: 未找到trig chunk")
        return
    
    # 提取GNSS记录
    gnss_records = extract_gnss_from_trig(trig_data)
    
    if not gnss_records:
        print("未找到有效的GNSS记录")
        return
    
    # 显示统计信息
    print(f"\n" + "="*80)
    print(f"提取结果统计:")
    print(f"  总记录数: {len(gnss_records):,}")
    
    if gnss_records:
        first = gnss_records[0]
        last = gnss_records[-1]
        
        print(f"  时间范围: {first['datetime']} 到 {last['datetime']}")
        print(f"  持续时间: {last['timestamp'] - first['timestamp']:.0f} 秒")
        
        lats = [r['latitude'] for r in gnss_records]
        lons = [r['longitude'] for r in gnss_records]
        
        print(f"  纬度范围: {min(lats):.6f}° 到 {max(lats):.6f}°")
        print(f"  经度范围: {min(lons):.6f}° 到 {max(lons):.6f}°")
        
        print(f"\n前5条记录:")
        for i, record in enumerate(gnss_records[:5]):
            print(f"    {i+1}. {record['datetime']} - "
                  f"纬度:{record['latitude']:.6f}°, 经度:{record['longitude']:.6f}°")
    
    # 保存数据
    save_gnss_data(gnss_records, "gnss_correct_format")
    
    print(f"\n✅ GNSS信息提取完成!")

if __name__ == "__main__":
    main()
