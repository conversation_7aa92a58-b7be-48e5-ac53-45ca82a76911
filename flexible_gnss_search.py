#!/usr/bin/env python3
"""
灵活的GNSS数据搜索
尝试不同的数据格式和结构来找到AbsoluteTimeStamp、Latitude、Longitude
"""

import struct
import os
import datetime
from typing import List, Dict, Any, Tu<PERSON>

def read_trig_chunk(filename: str) -> bytes:
    """读取trig chunk数据"""
    with open(filename, 'rb') as f:
        # 跳过RIFF头
        f.read(12)
        
        # 查找trig chunk
        while True:
            try:
                chunk_header = f.read(8)
                if len(chunk_header) < 8:
                    break
                
                chunk_id, chunk_size = struct.unpack('<4sI', chunk_header)
                chunk_id_str = chunk_id.decode('ascii', errors='ignore')
                
                if chunk_id_str == 'trig':
                    return f.read(chunk_size)
                else:
                    # 跳过这个chunk
                    f.seek(chunk_size, 1)
                    if chunk_size % 2 == 1:
                        f.read(1)  # 填充字节
                        
            except Exception as e:
                break
                
    return b''

def search_double_timestamps(data: bytes) -> List[Tuple[int, float]]:
    """搜索double格式的时间戳"""
    timestamps = []
    
    print("搜索double格式时间戳...")
    
    # 每8字节搜索一次
    for offset in range(0, len(data) - 8, 8):
        try:
            # 小端字节序
            timestamp = struct.unpack('<d', data[offset:offset+8])[0]
            
            # 验证时间戳范围 (2000-2030年)
            if 946684800 <= timestamp <= 1893456000:
                timestamps.append((offset, timestamp))
                
                if len(timestamps) <= 20:  # 显示前20个
                    dt = datetime.datetime.fromtimestamp(timestamp)
                    print(f"  偏移 {offset:8}: {timestamp:.6f} -> {dt}")
        
        except (struct.error, ValueError, OSError):
            continue
    
    print(f"找到 {len(timestamps)} 个可能的double时间戳")
    return timestamps

def search_float_coordinates_near_timestamp(data: bytes, timestamp_offset: int) -> List[Dict[str, Any]]:
    """在时间戳附近搜索float格式的坐标"""
    results = []
    
    # 在时间戳前后搜索坐标
    search_range = 64  # 在时间戳前后64字节范围内搜索
    
    start_offset = max(0, timestamp_offset - search_range)
    end_offset = min(len(data) - 4, timestamp_offset + 8 + search_range)
    
    timestamp = struct.unpack('<d', data[timestamp_offset:timestamp_offset+8])[0]
    dt = datetime.datetime.fromtimestamp(timestamp)
    
    for lat_offset in range(start_offset, end_offset, 4):
        if lat_offset + 4 > len(data):
            break
            
        try:
            latitude = struct.unpack('<f', data[lat_offset:lat_offset+4])[0]
            
            # 检查是否是有效纬度
            if -90 <= latitude <= 90:
                # 在纬度附近搜索经度
                for lon_offset in range(lat_offset + 4, min(lat_offset + 32, len(data) - 4), 4):
                    try:
                        longitude = struct.unpack('<f', data[lon_offset:lon_offset+4])[0]
                        
                        # 检查是否是有效经度
                        if -180 <= longitude <= 180:
                            result = {
                                'timestamp_offset': timestamp_offset,
                                'lat_offset': lat_offset,
                                'lon_offset': lon_offset,
                                'timestamp': timestamp,
                                'datetime': dt,
                                'latitude': latitude,
                                'longitude': longitude,
                                'distance_from_timestamp': lat_offset - timestamp_offset
                            }
                            results.append(result)
                    
                    except (struct.error, ValueError, OSError):
                        continue
        
        except (struct.error, ValueError, OSError):
            continue
    
    return results

def analyze_coordinate_patterns(coordinate_results: List[Dict[str, Any]]) -> Dict[str, Any]:
    """分析坐标模式"""
    if not coordinate_results:
        return {}
    
    # 按距离时间戳的偏移量分组
    distance_groups = {}
    for result in coordinate_results:
        distance = result['distance_from_timestamp']
        if distance not in distance_groups:
            distance_groups[distance] = []
        distance_groups[distance].append(result)
    
    # 找到最常见的偏移量
    most_common_distance = max(distance_groups.items(), key=lambda x: len(x[1]))
    
    analysis = {
        'total_results': len(coordinate_results),
        'distance_groups': {k: len(v) for k, v in distance_groups.items()},
        'most_common_distance': most_common_distance[0],
        'most_common_count': len(most_common_distance[1]),
        'sample_results': most_common_distance[1][:5]  # 前5个样本
    }
    
    print(f"\n坐标模式分析:")
    print(f"  总结果数: {analysis['total_results']}")
    print(f"  最常见的距离时间戳偏移: {analysis['most_common_distance']} 字节 ({analysis['most_common_count']} 次)")
    print(f"  距离分布:")
    for distance, count in sorted(analysis['distance_groups'].items())[:10]:
        print(f"    偏移 {distance:3}: {count:3} 次")
    
    return analysis

def try_different_record_formats(data: bytes) -> List[Dict[str, Any]]:
    """尝试不同的记录格式"""
    print("\n尝试不同的记录格式...")
    
    formats_to_try = [
        # (格式, 描述, 记录大小)
        ('<dff', 'double时间戳 + float纬度 + float经度', 16),
        ('<ddd', 'double时间戳 + double纬度 + double经度', 24),
        ('<Qff', 'uint64时间戳 + float纬度 + float经度', 16),
        ('<Idd', 'uint32时间戳 + double纬度 + double经度', 20),
        ('<Iff', 'uint32时间戳 + float纬度 + float经度', 12),
        ('<dffI', 'double时间戳 + float纬度 + float经度 + uint32', 20),
        ('<dffII', 'double时间戳 + float纬度 + float经度 + 2*uint32', 24),
        ('<Idff', 'uint32 + double时间戳 + float纬度 + float经度', 20),
        ('<IIdff', 'uint32 + uint32 + double时间戳 + float纬度 + float经度', 24),
    ]
    
    all_results = []
    
    for fmt, description, record_size in formats_to_try:
        print(f"\n尝试格式: {description} ({record_size}字节)")
        
        found_records = []
        
        # 尝试不同的起始偏移
        for start_offset in [0, 64, 128, 256, 512, 1024]:
            if start_offset >= len(data):
                continue
            
            # 测试前100条记录
            test_count = min(100, (len(data) - start_offset) // record_size)
            valid_count = 0
            
            for i in range(test_count):
                offset = start_offset + i * record_size
                if offset + record_size > len(data):
                    break
                
                try:
                    values = struct.unpack(fmt, data[offset:offset + record_size])
                    
                    # 根据格式解析时间戳和坐标
                    if fmt.startswith('<d'):  # double时间戳在开头
                        timestamp = values[0]
                        lat_idx, lon_idx = 1, 2
                    elif fmt.startswith('<Q'):  # uint64时间戳在开头
                        timestamp = values[0]
                        lat_idx, lon_idx = 1, 2
                    elif fmt.startswith('<I') and len(values) >= 3:  # uint32时间戳在开头
                        timestamp = values[0]
                        lat_idx, lon_idx = 1, 2
                    elif 'Id' in fmt:  # uint32 + double时间戳
                        timestamp = values[1]
                        lat_idx, lon_idx = 2, 3
                    elif 'IId' in fmt:  # uint32 + uint32 + double时间戳
                        timestamp = values[2]
                        lat_idx, lon_idx = 3, 4
                    else:
                        continue
                    
                    # 验证时间戳
                    if 946684800 <= timestamp <= 1893456000:
                        latitude = values[lat_idx]
                        longitude = values[lon_idx]
                        
                        # 验证坐标
                        if -90 <= latitude <= 90 and -180 <= longitude <= 180:
                            dt = datetime.datetime.fromtimestamp(timestamp)
                            
                            record = {
                                'format': description,
                                'offset': offset,
                                'timestamp': timestamp,
                                'datetime': dt,
                                'latitude': latitude,
                                'longitude': longitude,
                                'raw_values': values
                            }
                            found_records.append(record)
                            valid_count += 1
                
                except (struct.error, ValueError, OSError):
                    continue
            
            if valid_count > 0:
                print(f"  起始偏移 {start_offset}: 找到 {valid_count}/{test_count} 条有效记录")
                
                # 显示前几条记录
                for j, record in enumerate(found_records[:3]):
                    print(f"    {j+1}. {record['datetime']} - 纬度:{record['latitude']:.6f}, 经度:{record['longitude']:.6f}")
                
                all_results.extend(found_records)
                break  # 找到有效格式就不再尝试其他偏移
    
    return all_results

def main():
    filename = "0053_20250723_202435.part1.iq.wav"
    
    if not os.path.exists(filename):
        print(f"错误: 文件 {filename} 不存在")
        return
    
    print(f"灵活搜索GNSS数据: {filename}")
    print("="*60)
    
    # 读取trig chunk
    trig_data = read_trig_chunk(filename)
    
    if not trig_data:
        print("错误: 未找到trig chunk")
        return
    
    # 方法1: 搜索double时间戳，然后在附近寻找float坐标
    print("方法1: 搜索double时间戳 + 附近的float坐标")
    timestamps = search_double_timestamps(trig_data)
    
    if timestamps:
        print(f"\n分析前10个时间戳附近的坐标...")
        all_coordinate_results = []
        
        for i, (ts_offset, ts_value) in enumerate(timestamps[:10]):
            coord_results = search_float_coordinates_near_timestamp(trig_data, ts_offset)
            all_coordinate_results.extend(coord_results)
            
            if coord_results:
                print(f"  时间戳 {i+1} (偏移 {ts_offset}): 找到 {len(coord_results)} 个坐标组合")
        
        if all_coordinate_results:
            pattern_analysis = analyze_coordinate_patterns(all_coordinate_results)
            
            if pattern_analysis['sample_results']:
                print(f"\n最佳模式样本:")
                for j, result in enumerate(pattern_analysis['sample_results']):
                    print(f"  {j+1}. {result['datetime']} - 纬度:{result['latitude']:.6f}, 经度:{result['longitude']:.6f}")
    
    # 方法2: 尝试不同的固定记录格式
    print(f"\n" + "="*60)
    print("方法2: 尝试不同的固定记录格式")
    
    format_results = try_different_record_formats(trig_data)
    
    if format_results:
        print(f"\n找到 {len(format_results)} 条有效记录")
        
        # 按格式分组
        format_groups = {}
        for result in format_results:
            fmt = result['format']
            if fmt not in format_groups:
                format_groups[fmt] = []
            format_groups[fmt].append(result)
        
        print(f"\n按格式分组:")
        for fmt, records in format_groups.items():
            print(f"  {fmt}: {len(records)} 条记录")
        
        # 保存最有希望的结果
        if format_results:
            best_format = max(format_groups.items(), key=lambda x: len(x[1]))
            best_records = best_format[1]
            
            print(f"\n最佳格式: {best_format[0]} ({len(best_records)} 条记录)")
            
            # 保存结果
            csv_filename = "gnss_flexible_search.csv"
            with open(csv_filename, 'w', encoding='utf-8') as f:
                f.write("Format,Offset,AbsoluteTimeStamp,DateTime,Latitude,Longitude\n")
                for record in best_records:
                    f.write(f'"{record["format"]}",{record["offset"]},{record["timestamp"]:.6f},'
                           f'{record["datetime"]},{record["latitude"]:.8f},{record["longitude"]:.8f}\n')
            
            print(f"结果已保存到: {csv_filename}")
    
    else:
        print("\n未找到有效的GNSS数据格式")

if __name__ == "__main__":
    main()
