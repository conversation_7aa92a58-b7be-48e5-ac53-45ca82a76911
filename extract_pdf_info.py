#!/usr/bin/env python3
"""
提取PDF文档中的文本信息
"""

import os
import sys

def extract_pdf_text():
    """尝试提取PDF文本"""
    pdf_file = "iqs记录文件说明.pdf"
    
    if not os.path.exists(pdf_file):
        print(f"错误: 文件 {pdf_file} 不存在")
        return
    
    try:
        # 尝试使用PyPDF2
        import PyPDF2
        
        with open(pdf_file, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            text = ""
            
            print(f"PDF文件包含 {len(pdf_reader.pages)} 页")
            
            for page_num, page in enumerate(pdf_reader.pages):
                page_text = page.extract_text()
                text += f"\n=== 第 {page_num + 1} 页 ===\n"
                text += page_text
                print(f"第 {page_num + 1} 页文本:")
                print(page_text)
                print("-" * 50)
            
            # 保存提取的文本
            with open("pdf_extracted_text.txt", "w", encoding="utf-8") as f:
                f.write(text)
            
            print(f"文本已保存到 pdf_extracted_text.txt")
            
    except ImportError:
        print("PyPDF2 未安装，尝试使用 pdfplumber...")
        
        try:
            import pdfplumber
            
            with pdfplumber.open(pdf_file) as pdf:
                text = ""
                
                print(f"PDF文件包含 {len(pdf.pages)} 页")
                
                for page_num, page in enumerate(pdf.pages):
                    page_text = page.extract_text()
                    if page_text:
                        text += f"\n=== 第 {page_num + 1} 页 ===\n"
                        text += page_text
                        print(f"第 {page_num + 1} 页文本:")
                        print(page_text)
                        print("-" * 50)
                
                # 保存提取的文本
                with open("pdf_extracted_text.txt", "w", encoding="utf-8") as f:
                    f.write(text)
                
                print(f"文本已保存到 pdf_extracted_text.txt")
                
        except ImportError:
            print("pdfplumber 未安装，尝试使用 pymupdf...")
            
            try:
                import fitz  # PyMuPDF
                
                doc = fitz.open(pdf_file)
                text = ""
                
                print(f"PDF文件包含 {len(doc)} 页")
                
                for page_num in range(len(doc)):
                    page = doc.load_page(page_num)
                    page_text = page.get_text()
                    text += f"\n=== 第 {page_num + 1} 页 ===\n"
                    text += page_text
                    print(f"第 {page_num + 1} 页文本:")
                    print(page_text)
                    print("-" * 50)
                
                doc.close()
                
                # 保存提取的文本
                with open("pdf_extracted_text.txt", "w", encoding="utf-8") as f:
                    f.write(text)
                
                print(f"文本已保存到 pdf_extracted_text.txt")
                
            except ImportError:
                print("所有PDF库都未安装。请安装以下任一库:")
                print("pip install PyPDF2")
                print("pip install pdfplumber")
                print("pip install PyMuPDF")
                
                # 尝试手动分析PDF结构
                print("\n尝试手动分析PDF文件...")
                analyze_pdf_manually(pdf_file)
    
    except Exception as e:
        print(f"提取PDF文本时出错: {e}")

def analyze_pdf_manually(pdf_file):
    """手动分析PDF文件结构"""
    try:
        with open(pdf_file, 'rb') as f:
            content = f.read()
        
        # 查找文本流
        import re
        
        # 查找可能的文本内容
        text_patterns = [
            rb'BT\s+.*?ET',  # 文本对象
            rb'\((.*?)\)',   # 括号内的文本
            rb'<(.*?)>',     # 尖括号内的文本
        ]
        
        print("尝试从PDF中提取可能的文本内容:")
        
        for pattern in text_patterns:
            matches = re.findall(pattern, content, re.DOTALL)
            if matches:
                print(f"\n找到 {len(matches)} 个匹配项 (模式: {pattern}):")
                for i, match in enumerate(matches[:10]):  # 只显示前10个
                    try:
                        decoded = match.decode('utf-8', errors='ignore')
                        if len(decoded.strip()) > 0:
                            print(f"  {i+1}: {decoded[:100]}...")
                    except:
                        print(f"  {i+1}: {match[:100]}...")
        
        # 查找可能包含位置信息的关键词
        keywords = [
            b'AbsoluteTimeStamp',
            b'Latitude',
            b'Longitude',
            b'timestamp',
            b'latitude',
            b'longitude',
            b'GPS',
            b'position',
            b'location'
        ]
        
        print("\n查找关键词:")
        for keyword in keywords:
            if keyword in content:
                print(f"找到关键词: {keyword.decode()}")
                # 查找关键词周围的内容
                pos = content.find(keyword)
                start = max(0, pos - 100)
                end = min(len(content), pos + 200)
                context = content[start:end]
                print(f"  上下文: {context}")
        
    except Exception as e:
        print(f"手动分析PDF时出错: {e}")

if __name__ == "__main__":
    extract_pdf_text()
