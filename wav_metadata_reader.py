#!/usr/bin/env python3
"""
WAV文件元数据和GNSS信息读取器
根据常见的WAV文件格式规范，尝试读取AbsoluteTimeStamp、Latitude、Longitude信息
"""

import wave
import struct
import os
import datetime
from typing import Optional, Dict, Any

class WAVMetadataReader:
    def __init__(self, filename: str):
        self.filename = filename
        self.metadata = {}
        
    def read_wav_chunks(self):
        """读取WAV文件的所有chunk"""
        chunks = {}
        
        try:
            with open(self.filename, 'rb') as f:
                # 读取RIFF头
                riff_header = f.read(12)
                if len(riff_header) < 12:
                    return chunks
                
                riff_id, file_size, wave_id = struct.unpack('<4sI4s', riff_header)
                
                if riff_id != b'RIFF' or wave_id != b'WAVE':
                    print(f"不是有效的WAV文件")
                    return chunks
                
                print(f"RIFF文件大小: {file_size}")
                
                # 读取所有chunk
                while f.tell() < file_size + 8:
                    try:
                        chunk_header = f.read(8)
                        if len(chunk_header) < 8:
                            break
                        
                        chunk_id, chunk_size = struct.unpack('<4sI', chunk_header)
                        chunk_id_str = chunk_id.decode('ascii', errors='ignore')
                        
                        print(f"发现chunk: {chunk_id_str}, 大小: {chunk_size}")
                        
                        # 读取chunk数据
                        chunk_data = f.read(chunk_size)
                        chunks[chunk_id_str] = chunk_data
                        
                        # 如果chunk大小是奇数，跳过填充字节
                        if chunk_size % 2 == 1:
                            f.read(1)
                            
                    except Exception as e:
                        print(f"读取chunk时出错: {e}")
                        break
                        
        except Exception as e:
            print(f"读取WAV文件时出错: {e}")
            
        return chunks
    
    def parse_fmt_chunk(self, data: bytes) -> Dict[str, Any]:
        """解析fmt chunk"""
        if len(data) < 16:
            return {}
        
        fmt_info = {}
        
        # 基本格式信息
        audio_format, channels, sample_rate, byte_rate, block_align, bits_per_sample = struct.unpack('<HHIIHH', data[:16])
        
        fmt_info.update({
            'audio_format': audio_format,
            'channels': channels,
            'sample_rate': sample_rate,
            'byte_rate': byte_rate,
            'block_align': block_align,
            'bits_per_sample': bits_per_sample
        })
        
        # 如果有扩展信息
        if len(data) > 16:
            extension_size = struct.unpack('<H', data[16:18])[0]
            fmt_info['extension_size'] = extension_size
            
            if extension_size > 0 and len(data) >= 18 + extension_size:
                fmt_info['extension_data'] = data[18:18+extension_size]
        
        return fmt_info
    
    def parse_list_chunk(self, data: bytes) -> Dict[str, Any]:
        """解析LIST chunk，可能包含INFO信息"""
        if len(data) < 4:
            return {}
        
        list_type = data[:4]
        list_info = {'type': list_type.decode('ascii', errors='ignore')}
        
        if list_type == b'INFO':
            # 解析INFO子chunk
            offset = 4
            info_items = {}
            
            while offset < len(data) - 8:
                try:
                    subchunk_id = data[offset:offset+4]
                    subchunk_size = struct.unpack('<I', data[offset+4:offset+8])[0]
                    subchunk_data = data[offset+8:offset+8+subchunk_size]
                    
                    subchunk_id_str = subchunk_id.decode('ascii', errors='ignore')
                    
                    # 尝试解码为字符串
                    try:
                        value = subchunk_data.decode('utf-8', errors='ignore').rstrip('\x00')
                    except:
                        value = subchunk_data
                    
                    info_items[subchunk_id_str] = value
                    
                    offset += 8 + subchunk_size
                    if subchunk_size % 2 == 1:
                        offset += 1  # 填充字节
                        
                except Exception as e:
                    print(f"解析INFO子chunk时出错: {e}")
                    break
            
            list_info['info_items'] = info_items
        
        return list_info
    
    def search_gnss_data(self, chunks: Dict[str, bytes]) -> Dict[str, Any]:
        """在所有chunk中搜索GNSS相关数据"""
        gnss_data = {}
        
        # 搜索关键词
        keywords = {
            'timestamp': ['AbsoluteTimeStamp', 'timestamp', 'time', 'TIME'],
            'latitude': ['Latitude', 'latitude', 'lat', 'LAT'],
            'longitude': ['Longitude', 'longitude', 'lon', 'LON', 'lng', 'LNG']
        }
        
        for chunk_name, chunk_data in chunks.items():
            print(f"\n分析chunk: {chunk_name}")
            
            # 尝试作为文本搜索
            try:
                text_data = chunk_data.decode('utf-8', errors='ignore')
                
                for data_type, search_terms in keywords.items():
                    for term in search_terms:
                        if term in text_data:
                            print(f"  在文本中找到关键词: {term}")
                            # 尝试提取周围的数据
                            pos = text_data.find(term)
                            context = text_data[max(0, pos-50):pos+100]
                            print(f"    上下文: {context}")
                            gnss_data[f'{data_type}_context'] = context
            except:
                pass
            
            # 尝试作为二进制数据搜索
            for data_type, search_terms in keywords.items():
                for term in search_terms:
                    term_bytes = term.encode('utf-8')
                    if term_bytes in chunk_data:
                        print(f"  在二进制数据中找到关键词: {term}")
                        pos = chunk_data.find(term_bytes)
                        
                        # 尝试读取后续的数值数据
                        try:
                            # 跳过关键词，尝试读取后续数据
                            data_start = pos + len(term_bytes)
                            
                            # 尝试不同的数据格式
                            formats = [
                                ('<d', 8, 'double'),  # 64位浮点数
                                ('<f', 4, 'float'),   # 32位浮点数
                                ('<I', 4, 'uint32'),  # 32位无符号整数
                                ('<Q', 8, 'uint64'),  # 64位无符号整数
                            ]
                            
                            for fmt, size, desc in formats:
                                if data_start + size <= len(chunk_data):
                                    try:
                                        value = struct.unpack(fmt, chunk_data[data_start:data_start+size])[0]
                                        print(f"    可能的{desc}值: {value}")
                                        gnss_data[f'{data_type}_{desc}'] = value
                                    except:
                                        pass
                        except Exception as e:
                            print(f"    解析数值时出错: {e}")
        
        return gnss_data
    
    def parse_custom_chunks(self, chunks: Dict[str, bytes]) -> Dict[str, Any]:
        """解析可能包含GNSS数据的自定义chunk"""
        custom_data = {}
        
        # 常见的可能包含元数据的chunk类型
        metadata_chunks = ['bext', 'iXML', 'axml', 'cart', 'DGPS', 'GPS ', 'GNSS']
        
        for chunk_name in metadata_chunks:
            if chunk_name in chunks:
                print(f"\n发现可能的元数据chunk: {chunk_name}")
                chunk_data = chunks[chunk_name]
                
                # 尝试解析为XML
                try:
                    text = chunk_data.decode('utf-8', errors='ignore')
                    if '<' in text and '>' in text:
                        print(f"  可能是XML格式:")
                        print(f"  {text[:200]}...")
                        custom_data[f'{chunk_name}_xml'] = text
                except:
                    pass
                
                # 尝试解析为结构化数据
                if chunk_name == 'bext':
                    custom_data.update(self.parse_bext_chunk(chunk_data))
                
        return custom_data
    
    def parse_bext_chunk(self, data: bytes) -> Dict[str, Any]:
        """解析Broadcast Extension (bext) chunk"""
        bext_data = {}
        
        if len(data) < 602:
            return bext_data
        
        try:
            # bext chunk的标准格式
            description = data[0:256].decode('ascii', errors='ignore').rstrip('\x00')
            originator = data[256:288].decode('ascii', errors='ignore').rstrip('\x00')
            originator_reference = data[288:320].decode('ascii', errors='ignore').rstrip('\x00')
            origination_date = data[320:330].decode('ascii', errors='ignore').rstrip('\x00')
            origination_time = data[330:338].decode('ascii', errors='ignore').rstrip('\x00')
            
            time_reference_low, time_reference_high = struct.unpack('<II', data[338:346])
            time_reference = (time_reference_high << 32) | time_reference_low
            
            version = struct.unpack('<H', data[346:348])[0]
            
            bext_data.update({
                'description': description,
                'originator': originator,
                'originator_reference': originator_reference,
                'origination_date': origination_date,
                'origination_time': origination_time,
                'time_reference': time_reference,
                'version': version
            })
            
            print(f"  bext信息:")
            for key, value in bext_data.items():
                if value:
                    print(f"    {key}: {value}")
                    
        except Exception as e:
            print(f"解析bext chunk时出错: {e}")
        
        return bext_data
    
    def analyze(self) -> Dict[str, Any]:
        """完整分析WAV文件"""
        print(f"分析WAV文件: {self.filename}")
        print("=" * 60)
        
        # 基本文件信息
        file_size = os.path.getsize(self.filename)
        print(f"文件大小: {file_size:,} 字节")
        
        # 读取所有chunk
        chunks = self.read_wav_chunks()
        
        # 解析基本格式信息
        if 'fmt ' in chunks:
            fmt_info = self.parse_fmt_chunk(chunks['fmt '])
            self.metadata['format'] = fmt_info
            print(f"\n格式信息:")
            for key, value in fmt_info.items():
                print(f"  {key}: {value}")
        
        # 解析LIST chunk
        if 'LIST' in chunks:
            list_info = self.parse_list_chunk(chunks['LIST'])
            self.metadata['list'] = list_info
            print(f"\nLIST信息:")
            print(f"  类型: {list_info.get('type', 'unknown')}")
            if 'info_items' in list_info:
                for key, value in list_info['info_items'].items():
                    print(f"  {key}: {value}")
        
        # 搜索GNSS数据
        gnss_data = self.search_gnss_data(chunks)
        if gnss_data:
            self.metadata['gnss'] = gnss_data
            print(f"\nGNSS相关数据:")
            for key, value in gnss_data.items():
                print(f"  {key}: {value}")
        
        # 解析自定义chunk
        custom_data = self.parse_custom_chunks(chunks)
        if custom_data:
            self.metadata['custom'] = custom_data
        
        # 列出所有chunk
        print(f"\n所有chunk列表:")
        for chunk_name, chunk_data in chunks.items():
            print(f"  {chunk_name}: {len(chunk_data)} 字节")
        
        return self.metadata

def main():
    filename = "0053_20250723_202435.part1.iq.wav"
    
    if not os.path.exists(filename):
        print(f"错误: 文件 {filename} 不存在")
        return
    
    reader = WAVMetadataReader(filename)
    metadata = reader.analyze()
    
    print(f"\n" + "="*60)
    print(f"分析完成")
    
    # 总结GNSS信息
    if 'gnss' in metadata:
        print(f"\n发现的GNSS信息:")
        for key, value in metadata['gnss'].items():
            print(f"  {key}: {value}")
    else:
        print(f"\n未在标准位置找到GNSS信息")
        print(f"建议:")
        print(f"1. 检查是否有自定义的元数据格式")
        print(f"2. GNSS信息可能存储在数据部分而非头部")
        print(f"3. 可能需要特定的解码软件")

if __name__ == "__main__":
    main()
