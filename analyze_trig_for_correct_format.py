#!/usr/bin/env python3
"""
分析trig chunk寻找正确的GNSS格式
AbsoluteTimeStamp(double,8字节) + Latitude(float,4字节) + Longitude(float,4字节)
"""

import struct
import os
import datetime
from typing import List, Dict, Any, Tu<PERSON>

def read_trig_chunk(filename: str) -> bytes:
    """读取trig chunk数据"""
    with open(filename, 'rb') as f:
        # 跳过RIFF头
        f.read(12)
        
        # 查找trig chunk
        while True:
            try:
                chunk_header = f.read(8)
                if len(chunk_header) < 8:
                    break
                
                chunk_id, chunk_size = struct.unpack('<4sI', chunk_header)
                chunk_id_str = chunk_id.decode('ascii', errors='ignore')
                
                if chunk_id_str == 'trig':
                    return f.read(chunk_size)
                else:
                    # 跳过这个chunk
                    f.seek(chunk_size, 1)
                    if chunk_size % 2 == 1:
                        f.read(1)  # 填充字节
                        
            except Exception as e:
                break
                
    return b''

def search_gnss_pattern(data: bytes) -> List[Tuple[int, float, float, float]]:
    """
    在数据中搜索GNSS模式: double时间戳 + float纬度 + float经度
    返回: [(偏移量, 时间戳, 纬度, 经度), ...]
    """
    patterns = []
    
    print(f"搜索GNSS模式: double(8) + float(4) + float(4) = 16字节")
    print(f"数据总长度: {len(data):,} 字节")
    
    # 每4字节对齐搜索
    search_step = 4
    max_search = min(len(data) - 16, 1000000)  # 限制搜索范围以提高速度
    
    found_count = 0
    
    for offset in range(0, max_search, search_step):
        if offset + 16 > len(data):
            break
        
        try:
            # 尝试解析为 double + float + float
            timestamp, latitude, longitude = struct.unpack('<dff', data[offset:offset+16])
            
            # 验证时间戳 (Unix时间戳，2000-2030年)
            if 946684800 <= timestamp <= 1893456000:
                # 验证坐标范围
                if -90 <= latitude <= 90 and -180 <= longitude <= 180:
                    dt = datetime.datetime.fromtimestamp(timestamp)
                    patterns.append((offset, timestamp, latitude, longitude))
                    found_count += 1
                    
                    if found_count <= 10:  # 显示前10个找到的模式
                        print(f"  偏移 {offset:8}: {dt} - 纬度:{latitude:.6f}, 经度:{longitude:.6f}")
                    
                    if found_count >= 100:  # 找到足够多的模式就停止
                        break
        
        except (struct.error, ValueError, OSError):
            continue
    
    print(f"总共找到 {len(patterns)} 个有效的GNSS模式")
    return patterns

def analyze_pattern_spacing(patterns: List[Tuple[int, float, float, float]]) -> Dict[str, Any]:
    """分析模式之间的间距"""
    if len(patterns) < 2:
        return {}
    
    spacings = []
    for i in range(1, len(patterns)):
        spacing = patterns[i][0] - patterns[i-1][0]
        spacings.append(spacing)
    
    # 统计间距
    spacing_counts = {}
    for spacing in spacings:
        spacing_counts[spacing] = spacing_counts.get(spacing, 0) + 1
    
    # 找到最常见的间距
    most_common_spacing = max(spacing_counts.items(), key=lambda x: x[1])
    
    analysis = {
        'total_patterns': len(patterns),
        'spacings': spacings[:20],  # 前20个间距
        'spacing_counts': dict(sorted(spacing_counts.items(), key=lambda x: x[1], reverse=True)[:10]),
        'most_common_spacing': most_common_spacing[0],
        'most_common_count': most_common_spacing[1]
    }
    
    print(f"\n间距分析:")
    print(f"  总模式数: {analysis['total_patterns']}")
    print(f"  最常见间距: {analysis['most_common_spacing']} 字节 (出现 {analysis['most_common_count']} 次)")
    print(f"  前10个间距统计:")
    for spacing, count in list(analysis['spacing_counts'].items())[:10]:
        print(f"    {spacing:4} 字节: {count:4} 次")
    
    return analysis

def extract_regular_records(data: bytes, patterns: List[Tuple[int, float, float, float]], 
                          record_spacing: int) -> List[Dict[str, Any]]:
    """基于规律间距提取记录"""
    if not patterns:
        return []
    
    # 使用第一个模式作为起始点
    start_offset = patterns[0][0]
    
    records = []
    offset = start_offset
    record_index = 0
    
    print(f"\n基于间距 {record_spacing} 字节提取记录，起始偏移 {start_offset}")
    
    while offset + 16 <= len(data):
        try:
            timestamp, latitude, longitude = struct.unpack('<dff', data[offset:offset+16])
            
            # 验证数据有效性
            if (946684800 <= timestamp <= 1893456000 and
                -90 <= latitude <= 90 and
                -180 <= longitude <= 180):
                
                dt = datetime.datetime.fromtimestamp(timestamp)
                
                record = {
                    'index': record_index,
                    'offset': offset,
                    'timestamp': timestamp,
                    'datetime': dt,
                    'latitude': latitude,
                    'longitude': longitude
                }
                records.append(record)
                record_index += 1
            
            offset += record_spacing
            
        except (struct.error, ValueError, OSError):
            offset += record_spacing
            continue
    
    print(f"提取了 {len(records)} 条有效记录")
    return records

def analyze_data_around_patterns(data: bytes, patterns: List[Tuple[int, float, float, float]]):
    """分析模式周围的数据结构"""
    if not patterns:
        return
    
    print(f"\n分析前几个模式周围的数据结构:")
    
    for i, (offset, timestamp, latitude, longitude) in enumerate(patterns[:3]):
        print(f"\n模式 {i+1} (偏移 {offset}):")
        print(f"  时间戳: {datetime.datetime.fromtimestamp(timestamp)}")
        print(f"  坐标: {latitude:.6f}, {longitude:.6f}")
        
        # 显示前后32字节的数据
        start = max(0, offset - 32)
        end = min(len(data), offset + 48)
        context = data[start:end]
        
        print(f"  上下文数据 (偏移 {start} 到 {end}):")
        for j in range(0, len(context), 16):
            line = context[j:j+16]
            hex_str = ' '.join(f'{b:02x}' for b in line)
            ascii_str = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in line)
            abs_offset = start + j
            marker = " <-- GNSS" if abs_offset == offset else ""
            print(f"    {abs_offset:6}: {hex_str:<48} {ascii_str}{marker}")

def main():
    filename = "0053_20250723_202435.part1.iq.wav"
    
    if not os.path.exists(filename):
        print(f"错误: 文件 {filename} 不存在")
        return
    
    print(f"分析trig chunk寻找正确的GNSS格式: {filename}")
    print("目标格式: AbsoluteTimeStamp(double,8字节) + Latitude(float,4字节) + Longitude(float,4字节)")
    print("="*80)
    
    # 读取trig chunk
    trig_data = read_trig_chunk(filename)
    
    if not trig_data:
        print("错误: 未找到trig chunk")
        return
    
    # 搜索GNSS模式
    patterns = search_gnss_pattern(trig_data)
    
    if not patterns:
        print("未找到符合格式的GNSS数据")
        
        # 尝试其他可能的格式组合
        print("\n尝试其他可能的格式...")
        
        # 尝试不同的字节序
        print("尝试大端字节序...")
        for offset in range(0, min(len(trig_data) - 16, 100000), 4):
            try:
                timestamp, latitude, longitude = struct.unpack('>dff', trig_data[offset:offset+16])
                if (946684800 <= timestamp <= 1893456000 and
                    -90 <= latitude <= 90 and -180 <= longitude <= 180):
                    dt = datetime.datetime.fromtimestamp(timestamp)
                    print(f"  大端格式在偏移 {offset}: {dt} - {latitude:.6f}, {longitude:.6f}")
                    break
            except:
                continue
        
        return
    
    # 分析模式间距
    spacing_analysis = analyze_pattern_spacing(patterns)
    
    # 分析模式周围的数据
    analyze_data_around_patterns(trig_data, patterns)
    
    # 如果找到了规律的间距，尝试提取所有记录
    if spacing_analysis and spacing_analysis['most_common_spacing'] > 16:
        regular_spacing = spacing_analysis['most_common_spacing']
        print(f"\n尝试使用间距 {regular_spacing} 字节提取所有记录...")
        
        records = extract_regular_records(trig_data, patterns, regular_spacing)
        
        if records:
            # 保存结果
            csv_filename = "gnss_correct_extracted.csv"
            with open(csv_filename, 'w', encoding='utf-8') as f:
                f.write("Index,Offset,AbsoluteTimeStamp,DateTime,Latitude,Longitude\n")
                for record in records:
                    f.write(f"{record['index']},{record['offset']},{record['timestamp']:.6f},"
                           f"{record['datetime']},{record['latitude']:.8f},{record['longitude']:.8f}\n")
            
            print(f"结果已保存到: {csv_filename}")
            
            # 显示统计
            if records:
                first = records[0]
                last = records[-1]
                lats = [r['latitude'] for r in records]
                lons = [r['longitude'] for r in records]
                
                print(f"\n提取结果统计:")
                print(f"  记录数: {len(records):,}")
                print(f"  时间范围: {first['datetime']} 到 {last['datetime']}")
                print(f"  纬度范围: {min(lats):.6f}° 到 {max(lats):.6f}°")
                print(f"  经度范围: {min(lons):.6f}° 到 {max(lons):.6f}°")

if __name__ == "__main__":
    main()
