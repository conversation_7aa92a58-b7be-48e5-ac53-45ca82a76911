#!/usr/bin/env python3
"""
使用正确的格式重新解释之前找到的GNSS数据
基于之前成功找到的偏移位置，用double+float+float格式重新解析
"""

import struct
import os
import datetime
from typing import List, Dict, Any

def read_trig_chunk(filename: str) -> bytes:
    """读取trig chunk数据"""
    with open(filename, 'rb') as f:
        # 跳过RIFF头
        f.read(12)
        
        # 查找trig chunk
        while True:
            try:
                chunk_header = f.read(8)
                if len(chunk_header) < 8:
                    break
                
                chunk_id, chunk_size = struct.unpack('<4sI', chunk_header)
                chunk_id_str = chunk_id.decode('ascii', errors='ignore')
                
                if chunk_id_str == 'trig':
                    return f.read(chunk_size)
                else:
                    # 跳过这个chunk
                    f.seek(chunk_size, 1)
                    if chunk_size % 2 == 1:
                        f.read(1)  # 填充字节
                        
            except Exception as e:
                break
                
    return b''

def reinterpret_known_locations(data: bytes) -> List[Dict[str, Any]]:
    """
    基于之前分析的结果，在已知的有效位置重新解释数据
    之前我们发现在偏移708等位置有有效的时间戳和坐标数据
    """
    print("重新解释已知位置的数据...")
    
    # 之前分析中发现的有效偏移位置
    known_offsets = [708, 736, 764, 792, 820]  # 这些是之前找到时间戳的位置
    
    records = []
    
    for base_offset in known_offsets:
        print(f"\n分析偏移 {base_offset} 附近的数据:")
        
        # 在这个偏移附近尝试不同的格式组合
        for offset_adj in range(-32, 33, 4):  # 前后32字节，4字节对齐
            test_offset = base_offset + offset_adj
            
            if test_offset < 0 or test_offset + 16 > len(data):
                continue
            
            try:
                # 尝试 double(8) + float(4) + float(4) 格式
                timestamp, latitude, longitude = struct.unpack('<dff', data[test_offset:test_offset+16])
                
                # 验证时间戳
                if 946684800 <= timestamp <= 1893456000:
                    # 验证坐标
                    if -90 <= latitude <= 90 and -180 <= longitude <= 180:
                        dt = datetime.datetime.fromtimestamp(timestamp)
                        
                        record = {
                            'offset': test_offset,
                            'timestamp': timestamp,
                            'datetime': dt,
                            'latitude': latitude,
                            'longitude': longitude,
                            'format': 'double_timestamp + float_lat + float_lon'
                        }
                        records.append(record)
                        
                        print(f"  偏移 {test_offset}: {dt} - 纬度:{latitude:.8f}, 经度:{longitude:.8f}")
                
                # 也尝试其他可能的组合
                # 尝试将之前的uint32数据重新解释为double的一部分
                if test_offset + 12 <= len(data):
                    # 读取12字节，尝试不同的解释
                    raw_12 = data[test_offset:test_offset+12]
                    
                    # 尝试: uint32时间戳 + double纬度 (但这需要16字节)
                    if test_offset + 16 <= len(data):
                        raw_16 = data[test_offset:test_offset+16]
                        
                        # 尝试: uint32 + double + float 或其他组合
                        try:
                            ts_uint32 = struct.unpack('<I', raw_16[:4])[0]
                            if 946684800 <= ts_uint32 <= 1893456000:
                                # 后面8字节作为double纬度
                                lat_double = struct.unpack('<d', raw_16[4:12])[0]
                                # 最后4字节作为float经度
                                lon_float = struct.unpack('<f', raw_16[12:16])[0]
                                
                                if -90 <= lat_double <= 90 and -180 <= lon_float <= 180:
                                    dt = datetime.datetime.fromtimestamp(ts_uint32)
                                    
                                    record = {
                                        'offset': test_offset,
                                        'timestamp': ts_uint32,
                                        'datetime': dt,
                                        'latitude': lat_double,
                                        'longitude': lon_float,
                                        'format': 'uint32_timestamp + double_lat + float_lon'
                                    }
                                    records.append(record)
                                    
                                    print(f"  偏移 {test_offset} (混合): {dt} - 纬度:{lat_double:.8f}, 经度:{lon_float:.8f}")
                        except:
                            pass
            
            except (struct.error, ValueError, OSError):
                continue
    
    return records

def search_with_known_pattern(data: bytes) -> List[Dict[str, Any]]:
    """
    基于之前发现的28字节记录模式，重新用正确格式解析
    """
    print("\n基于已知的28字节记录模式重新解析...")
    
    # 之前发现的参数
    header_size = 512
    record_size = 28
    
    if len(data) <= header_size:
        return []
    
    data_section = data[header_size:]
    num_records = len(data_section) // record_size
    
    print(f"数据段: {len(data_section)} 字节, 记录数: {num_records}")
    
    records = []
    
    # 在每个28字节记录中寻找double+float+float模式
    for i in range(min(1000, num_records)):  # 测试前1000条记录
        record_start = header_size + i * record_size
        record_data = data[record_start:record_start + record_size]
        
        # 在28字节记录中的不同位置尝试16字节的double+float+float格式
        for offset_in_record in range(0, record_size - 16 + 1, 4):
            try:
                test_data = record_data[offset_in_record:offset_in_record + 16]
                timestamp, latitude, longitude = struct.unpack('<dff', test_data)
                
                # 验证数据
                if (946684800 <= timestamp <= 1893456000 and
                    -90 <= latitude <= 90 and
                    -180 <= longitude <= 180):
                    
                    dt = datetime.datetime.fromtimestamp(timestamp)
                    
                    record = {
                        'record_index': i,
                        'offset': record_start + offset_in_record,
                        'offset_in_record': offset_in_record,
                        'timestamp': timestamp,
                        'datetime': dt,
                        'latitude': latitude,
                        'longitude': longitude,
                        'format': 'double_timestamp + float_lat + float_lon (in 28byte record)'
                    }
                    records.append(record)
                    
                    if len(records) <= 10:  # 显示前10个
                        print(f"  记录 {i}, 内偏移 {offset_in_record}: {dt} - 纬度:{latitude:.8f}, 经度:{longitude:.8f}")
            
            except (struct.error, ValueError, OSError):
                continue
    
    return records

def convert_previous_data_to_correct_format(data: bytes) -> List[Dict[str, Any]]:
    """
    将之前找到的uint32格式数据转换为double+float+float格式
    """
    print("\n尝试转换之前的数据格式...")
    
    # 之前成功的格式参数
    header_size = 512
    record_size = 28
    
    if len(data) <= header_size:
        return []
    
    data_section = data[header_size:]
    num_records = len(data_section) // record_size
    
    records = []
    
    for i in range(min(1000, num_records)):
        record_start = header_size + i * record_size
        record_data = data[record_start:record_start + record_size]
        
        try:
            # 解析为7个uint32 (之前成功的格式)
            uint32_values = struct.unpack('<7I', record_data)
            
            # 检查第一个uint32是否是时间戳
            timestamp_uint32 = uint32_values[0]
            
            if 946684800 <= timestamp_uint32 <= 1893456000:
                # 将uint32时间戳转换为double
                timestamp_double = float(timestamp_uint32)
                
                # 尝试将后续的uint32值重新解释为float坐标
                for lat_idx in range(1, 6):
                    for lon_idx in range(lat_idx + 1, 7):
                        try:
                            # 将uint32重新解释为float
                            lat_bytes = struct.pack('<I', uint32_values[lat_idx])
                            lon_bytes = struct.pack('<I', uint32_values[lon_idx])
                            
                            latitude = struct.unpack('<f', lat_bytes)[0]
                            longitude = struct.unpack('<f', lon_bytes)[0]
                            
                            # 验证坐标
                            if -90 <= latitude <= 90 and -180 <= longitude <= 180:
                                dt = datetime.datetime.fromtimestamp(timestamp_double)
                                
                                record = {
                                    'record_index': i,
                                    'offset': record_start,
                                    'timestamp': timestamp_double,
                                    'datetime': dt,
                                    'latitude': latitude,
                                    'longitude': longitude,
                                    'format': f'converted_uint32_to_double+float (pos {lat_idx},{lon_idx})',
                                    'raw_values': uint32_values
                                }
                                records.append(record)
                                
                                if len(records) <= 10:
                                    print(f"  记录 {i}: {dt} - 纬度:{latitude:.8f}, 经度:{longitude:.8f}")
                                
                                break  # 找到有效坐标就跳出
                        except:
                            continue
                    
                    if records and records[-1]['record_index'] == i:
                        break  # 已经找到这条记录的有效解释
        
        except (struct.error, ValueError, OSError):
            continue
    
    return records

def main():
    filename = "0053_20250723_202435.part1.iq.wav"
    
    if not os.path.exists(filename):
        print(f"错误: 文件 {filename} 不存在")
        return
    
    print(f"使用正确格式重新解释GNSS数据: {filename}")
    print("目标格式: AbsoluteTimeStamp(double,8字节) + Latitude(float,4字节) + Longitude(float,4字节)")
    print("="*80)
    
    # 读取trig chunk
    trig_data = read_trig_chunk(filename)
    
    if not trig_data:
        print("错误: 未找到trig chunk")
        return
    
    all_records = []
    
    # 方法1: 重新解释已知位置
    method1_records = reinterpret_known_locations(trig_data)
    all_records.extend(method1_records)
    
    # 方法2: 在28字节记录中搜索
    method2_records = search_with_known_pattern(trig_data)
    all_records.extend(method2_records)
    
    # 方法3: 转换之前的数据格式
    method3_records = convert_previous_data_to_correct_format(trig_data)
    all_records.extend(method3_records)
    
    # 去重并排序
    unique_records = []
    seen_offsets = set()
    
    for record in all_records:
        if record['offset'] not in seen_offsets:
            unique_records.append(record)
            seen_offsets.add(record['offset'])
    
    unique_records.sort(key=lambda x: x['timestamp'])
    
    print(f"\n" + "="*80)
    print(f"重新解释结果:")
    print(f"  总共找到 {len(unique_records)} 条唯一记录")
    
    if unique_records:
        # 按格式分组
        format_groups = {}
        for record in unique_records:
            fmt = record['format']
            if fmt not in format_groups:
                format_groups[fmt] = []
            format_groups[fmt].append(record)
        
        print(f"\n按格式分组:")
        for fmt, records in format_groups.items():
            print(f"  {fmt}: {len(records)} 条记录")
        
        # 显示前几条记录
        print(f"\n前10条记录:")
        for i, record in enumerate(unique_records[:10]):
            print(f"  {i+1}. {record['datetime']} - 纬度:{record['latitude']:.8f}, 经度:{record['longitude']:.8f}")
            print(f"      格式: {record['format']}")
        
        # 保存结果
        csv_filename = "gnss_reinterpreted_correct_format.csv"
        with open(csv_filename, 'w', encoding='utf-8') as f:
            f.write("Offset,AbsoluteTimeStamp,DateTime,Latitude,Longitude,Format\n")
            for record in unique_records:
                f.write(f"{record['offset']},{record['timestamp']:.6f},"
                       f"{record['datetime']},{record['latitude']:.8f},{record['longitude']:.8f},"
                       f'"{record["format"]}"\n')
        
        print(f"\n结果已保存到: {csv_filename}")
        
        # 统计信息
        if unique_records:
            first = unique_records[0]
            last = unique_records[-1]
            lats = [r['latitude'] for r in unique_records]
            lons = [r['longitude'] for r in unique_records]
            
            print(f"\n统计信息:")
            print(f"  时间范围: {first['datetime']} 到 {last['datetime']}")
            print(f"  纬度范围: {min(lats):.8f}° 到 {max(lats):.8f}°")
            print(f"  经度范围: {min(lons):.8f}° 到 {max(lons):.8f}°")
    
    else:
        print("未找到符合正确格式的GNSS数据")

if __name__ == "__main__":
    main()
