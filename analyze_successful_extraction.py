#!/usr/bin/env python3
"""
分析之前成功提取的GNSS数据，确定正确的数据格式
"""

import struct
import os
import datetime
from typing import List, Dict, Any

def read_trig_chunk(filename: str) -> bytes:
    """读取trig chunk数据"""
    with open(filename, 'rb') as f:
        # 跳过RIFF头
        f.read(12)
        
        # 查找trig chunk
        while True:
            try:
                chunk_header = f.read(8)
                if len(chunk_header) < 8:
                    break
                
                chunk_id, chunk_size = struct.unpack('<4sI', chunk_header)
                chunk_id_str = chunk_id.decode('ascii', errors='ignore')
                
                if chunk_id_str == 'trig':
                    return f.read(chunk_size)
                else:
                    # 跳过这个chunk
                    f.seek(chunk_size, 1)
                    if chunk_size % 2 == 1:
                        f.read(1)  # 填充字节
                        
            except Exception as e:
                break
                
    return b''

def analyze_known_successful_offsets(data: bytes):
    """分析之前成功提取数据的偏移位置"""
    print("分析之前成功的数据提取位置...")
    
    # 之前成功的参数
    header_size = 512
    record_size = 28
    
    # 已知的成功时间戳: 1482184792 (2016-12-20 05:59:52)
    target_timestamp = 1482184792
    
    print(f"寻找时间戳 {target_timestamp} ({datetime.datetime.fromtimestamp(target_timestamp)})")
    
    # 在数据中搜索这个时间戳
    timestamp_bytes = struct.pack('<I', target_timestamp)  # uint32格式
    timestamp_bytes_double = struct.pack('<d', float(target_timestamp))  # double格式
    
    print(f"uint32格式: {timestamp_bytes.hex()}")
    print(f"double格式: {timestamp_bytes_double.hex()}")
    
    # 搜索uint32格式的时间戳
    uint32_positions = []
    offset = 0
    while True:
        pos = data.find(timestamp_bytes, offset)
        if pos == -1:
            break
        uint32_positions.append(pos)
        offset = pos + 1
    
    print(f"找到 {len(uint32_positions)} 个uint32格式的时间戳位置")
    
    # 搜索double格式的时间戳
    double_positions = []
    offset = 0
    while True:
        pos = data.find(timestamp_bytes_double, offset)
        if pos == -1:
            break
        double_positions.append(pos)
        offset = pos + 1
    
    print(f"找到 {len(double_positions)} 个double格式的时间戳位置")
    
    # 分析uint32位置周围的数据
    print(f"\n分析uint32时间戳位置:")
    for i, pos in enumerate(uint32_positions[:10]):  # 只分析前10个
        print(f"\n位置 {i+1}: 偏移 {pos}")
        
        # 显示周围32字节的数据
        start = max(0, pos - 16)
        end = min(len(data), pos + 32)
        context = data[start:end]
        
        print(f"  上下文数据:")
        for j in range(0, len(context), 16):
            line = context[j:j+16]
            hex_str = ' '.join(f'{b:02x}' for b in line)
            abs_offset = start + j
            marker = " <-- 时间戳" if abs_offset == pos else ""
            print(f"    {abs_offset:6}: {hex_str:<48}{marker}")
        
        # 尝试解析后续数据为不同格式
        if pos + 16 <= len(data):
            print(f"  尝试解析后续数据:")
            
            # 方法1: uint32时间戳 + float纬度 + float经度
            try:
                ts, lat_f, lon_f = struct.unpack('<Iff', data[pos:pos+12])
                print(f"    uint32+float+float: 时间戳={ts}, 纬度={lat_f}, 经度={lon_f}")
            except:
                pass
            
            # 方法2: uint32时间戳 + double纬度 + float经度  
            try:
                ts = struct.unpack('<I', data[pos:pos+4])[0]
                lat_d = struct.unpack('<d', data[pos+4:pos+12])[0]
                lon_f = struct.unpack('<f', data[pos+12:pos+16])[0]
                print(f"    uint32+double+float: 时间戳={ts}, 纬度={lat_d}, 经度={lon_f}")
            except:
                pass
            
            # 方法3: 检查是否在28字节记录的边界上
            record_start = (pos - header_size) // record_size * record_size + header_size
            offset_in_record = pos - record_start
            
            if record_start >= header_size and record_start + record_size <= len(data):
                print(f"    在28字节记录中的位置: 记录起始={record_start}, 内偏移={offset_in_record}")
                
                # 解析整个28字节记录
                record_data = data[record_start:record_start + record_size]
                try:
                    values = struct.unpack('<7I', record_data)
                    print(f"    28字节记录(7个uint32): {values}")
                    
                    # 检查哪些值可能是坐标 (之前成功的方法)
                    for idx, val in enumerate(values):
                        if idx == 0:  # 时间戳
                            continue
                        
                        # 尝试不同的缩放因子
                        for scale in [1e-8, 1e-7, 1e-6]:
                            scaled = val * scale
                            if -90 <= scaled <= 90 or -180 <= scaled <= 180:
                                print(f"      位置{idx}: {val} * {scale} = {scaled} (可能的坐标)")
                except:
                    pass
    
    # 分析double位置周围的数据
    if double_positions:
        print(f"\n分析double时间戳位置:")
        for i, pos in enumerate(double_positions[:5]):  # 只分析前5个
            print(f"\n位置 {i+1}: 偏移 {pos}")
            
            if pos + 16 <= len(data):
                try:
                    # double时间戳 + float纬度 + float经度
                    ts_d, lat_f, lon_f = struct.unpack('<dff', data[pos:pos+16])
                    print(f"    double+float+float: 时间戳={ts_d}, 纬度={lat_f}, 经度={lon_f}")
                    
                    if 946684800 <= ts_d <= 1893456000:
                        dt = datetime.datetime.fromtimestamp(ts_d)
                        print(f"      时间: {dt}")
                        if -90 <= lat_f <= 90 and -180 <= lon_f <= 180:
                            print(f"      有效坐标!")
                except:
                    pass

def extract_with_correct_format_based_on_analysis(data: bytes) -> List[Dict[str, Any]]:
    """基于分析结果，用正确格式提取数据"""
    print(f"\n基于分析结果提取GNSS数据...")
    
    records = []
    
    # 基于之前成功的方法，但寻找正确的double+float+float格式
    header_size = 512
    record_size = 28
    
    if len(data) <= header_size:
        return records
    
    data_section = data[header_size:]
    num_records = len(data_section) // record_size
    
    print(f"分析 {num_records} 条28字节记录...")
    
    for i in range(min(10000, num_records)):  # 分析前10000条记录
        record_start = header_size + i * record_size
        record_data = data[record_start:record_start + record_size]
        
        # 在28字节记录中寻找double+float+float模式
        for offset in range(0, record_size - 16 + 1):
            try:
                test_data = record_data[offset:offset + 16]
                
                # 尝试double+float+float
                timestamp, latitude, longitude = struct.unpack('<dff', test_data)
                
                # 验证时间戳
                if 946684800 <= timestamp <= 1893456000:
                    # 验证坐标
                    if -90 <= latitude <= 90 and -180 <= longitude <= 180:
                        # 排除全零坐标
                        if latitude != 0.0 or longitude != 0.0:
                            dt = datetime.datetime.fromtimestamp(timestamp)
                            
                            record = {
                                'record_index': i,
                                'offset': record_start + offset,
                                'timestamp': timestamp,
                                'datetime': dt,
                                'latitude': latitude,
                                'longitude': longitude,
                                'format': 'double_timestamp + float_lat + float_lon'
                            }
                            records.append(record)
                            
                            if len(records) <= 20:
                                print(f"  记录 {i}, 偏移 {offset}: {dt} - 纬度:{latitude:.8f}, 经度:{longitude:.8f}")
            
            except (struct.error, ValueError, OSError):
                continue
        
        # 也尝试其他可能的组合
        try:
            # 解析为7个uint32，然后重新组合
            uint32_values = struct.unpack('<7I', record_data)
            
            # 检查第一个是否是时间戳
            if 946684800 <= uint32_values[0] <= 1893456000:
                # 尝试将两个连续的uint32组合成一个double
                for ts_start in range(0, 6):  # 时间戳可能的起始位置
                    if ts_start + 2 < 7:  # 需要至少3个uint32 (2个组成double + 1个float)
                        try:
                            # 将两个uint32组合成double
                            ts_bytes = struct.pack('<II', uint32_values[ts_start], uint32_values[ts_start + 1])
                            timestamp = struct.unpack('<d', ts_bytes)[0]
                            
                            if 946684800 <= timestamp <= 1893456000:
                                # 后续的uint32作为float坐标
                                for coord_idx in range(ts_start + 2, 7):
                                    if coord_idx + 1 < 7:
                                        lat_bytes = struct.pack('<I', uint32_values[coord_idx])
                                        lon_bytes = struct.pack('<I', uint32_values[coord_idx + 1])
                                        
                                        latitude = struct.unpack('<f', lat_bytes)[0]
                                        longitude = struct.unpack('<f', lon_bytes)[0]
                                        
                                        if (-90 <= latitude <= 90 and -180 <= longitude <= 180 and
                                            (latitude != 0.0 or longitude != 0.0)):
                                            
                                            dt = datetime.datetime.fromtimestamp(timestamp)
                                            
                                            record = {
                                                'record_index': i,
                                                'offset': record_start,
                                                'timestamp': timestamp,
                                                'datetime': dt,
                                                'latitude': latitude,
                                                'longitude': longitude,
                                                'format': f'combined_uint32_to_double+float (pos {ts_start},{coord_idx})'
                                            }
                                            records.append(record)
                                            
                                            if len(records) <= 20:
                                                print(f"  记录 {i} (组合): {dt} - 纬度:{latitude:.8f}, 经度:{longitude:.8f}")
                                            break
                        except:
                            continue
        except:
            continue
    
    return records

def main():
    filename = "0053_20250723_202435.part1.iq.wav"
    
    if not os.path.exists(filename):
        print(f"错误: 文件 {filename} 不存在")
        return
    
    print(f"分析成功提取的GNSS数据格式: {filename}")
    print("="*80)
    
    # 读取trig chunk
    trig_data = read_trig_chunk(filename)
    
    if not trig_data:
        print("错误: 未找到trig chunk")
        return
    
    # 分析已知成功的偏移位置
    analyze_known_successful_offsets(trig_data)
    
    # 基于分析结果提取数据
    records = extract_with_correct_format_based_on_analysis(trig_data)
    
    if records:
        print(f"\n找到 {len(records)} 条符合正确格式的GNSS记录")
        
        # 保存结果
        csv_filename = "gnss_correct_format_final.csv"
        with open(csv_filename, 'w', encoding='utf-8') as f:
            f.write("RecordIndex,Offset,AbsoluteTimeStamp,DateTime,Latitude,Longitude,Format\n")
            for record in records:
                f.write(f"{record['record_index']},{record['offset']},{record['timestamp']:.6f},"
                       f"{record['datetime']},{record['latitude']:.8f},{record['longitude']:.8f},"
                       f'"{record["format"]}"\n')
        
        print(f"结果已保存到: {csv_filename}")
        
        # 统计信息
        if records:
            first = records[0]
            last = records[-1]
            lats = [r['latitude'] for r in records]
            lons = [r['longitude'] for r in records]
            
            print(f"\n统计信息:")
            print(f"  记录数: {len(records)}")
            print(f"  时间范围: {first['datetime']} 到 {last['datetime']}")
            print(f"  纬度范围: {min(lats):.8f}° 到 {max(lats):.8f}°")
            print(f"  经度范围: {min(lons):.8f}° 到 {max(lons):.8f}°")
    
    else:
        print("\n未找到符合正确格式的GNSS记录")
        print("可能的原因:")
        print("1. 数据格式与预期不同")
        print("2. 需要特殊的解码算法")
        print("3. 数据可能经过了压缩或加密")

if __name__ == "__main__":
    main()
