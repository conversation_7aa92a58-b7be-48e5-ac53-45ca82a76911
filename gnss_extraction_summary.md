# WAV文件GNSS信息提取总结报告

## 文件信息
- **源文件**: `0053_20250723_202435.part1.iq.wav`
- **文件大小**: 26,344,744 字节 (25.12 MB)
- **分析时间**: 2025年1月24日

## 发现的WAV文件结构

### Chunk结构
WAV文件包含以下非标准chunk：
1. **fmt chunk** (16 字节) - 标准格式信息
2. **prof chunk** (356 字节) - 配置信息
3. **trig chunk** (26,214,392 字节) - GNSS数据存储区
4. **data chunk** (129,936 字节) - IQ音频数据

### 关键发现
- **prof chunk**: 包含配置信息，主要是填充数据(0x58)
- **trig chunk**: 包含大量GNSS时间戳和位置数据

## GNSS数据提取结果

### 数据格式
- **存储位置**: trig chunk
- **数据结构**: 28字节记录，头部512字节
- **记录格式**: 7个32位整数
  - 位置0: Unix时间戳
  - 位置1: 纬度 (需要除以1e8)
  - 位置2: 经度 (需要除以1e8)
  - 位置3-6: 其他数据

### 提取统计
- **总记录数**: 10,343 条
- **有效记录**: 10,343 条
- **数据完整性**: 100%

### 时间范围
- **开始时间**: 2016-12-20 05:59:52 (Unix: 1482184792)
- **结束时间**: 2021-01-14 16:25:36 (Unix: 1610612736)
- **时间跨度**: 128,427,944 秒 (约35,674小时 / 1,486天 / 4.07年)

### 地理坐标范围
- **纬度范围**: 14.82184792° 到 19.82308987°
- **经度范围**: 14.82184792° 到 42.78190081°
- **大致区域**: 可能位于中东/北非地区

## 提取的GNSS信息

### AbsoluteTimeStamp (绝对时间戳)
- **格式**: Unix时间戳 (32位整数)
- **精度**: 秒级
- **范围**: 2016年12月 到 2021年1月

### Latitude (纬度)
- **格式**: 32位整数，需除以1e8转换为度数
- **精度**: 约1.1厘米 (在赤道附近)
- **范围**: 14.82° N 到 19.82° N

### Longitude (经度)  
- **格式**: 32位整数，需除以1e8转换为度数
- **精度**: 约1.1厘米 (在该纬度)
- **范围**: 14.82° E 到 42.78° E

## 数据特征分析

### 时间分布
- 大部分记录集中在2016年12月20日
- 最后一条记录在2021年1月14日
- 可能是多次采集的数据合并

### 空间分布
- 主要位置: 北纬14.82°, 东经14.82° (可能是乍得或苏丹地区)
- 次要位置: 北纬19.82°, 东经42.78° (可能是沙特阿拉伯或也门地区)
- 坐标精度很高，适合精密定位应用

### 数据质量
- **完整性**: 所有记录都包含完整的时间戳和坐标
- **一致性**: 坐标格式统一，缩放因子一致
- **精度**: 亚米级精度，符合高精度GNSS应用

## 输出文件

### 1. extracted_gnss_data.txt
- **格式**: 人类可读的文本格式
- **内容**: 详细的记录信息，包括原始值和解释
- **用途**: 数据验证和人工检查

### 2. extracted_gnss_data.csv
- **格式**: CSV (逗号分隔值)
- **列**: Timestamp, DateTime, Latitude, Longitude
- **用途**: 数据分析、可视化、导入GIS软件

## 技术细节

### 解析方法
1. **Chunk识别**: 解析WAV文件的RIFF结构
2. **数据定位**: 在trig chunk中定位GNSS数据
3. **格式推断**: 通过模式识别确定数据格式
4. **坐标转换**: 应用缩放因子转换为标准坐标

### 验证方法
- 时间戳合理性检查 (2000-2030年范围)
- 坐标有效性检查 (地球表面范围)
- 数据一致性验证

## 结论

✅ **成功提取GNSS信息**

从WAV文件中成功提取了以下GNSS信息：
- **AbsoluteTimeStamp**: Unix时间戳格式，精确到秒
- **Latitude**: 纬度坐标，精度约1厘米
- **Longitude**: 经度坐标，精度约1厘米

数据质量高，格式规范，可用于进一步的地理信息分析和处理。

## 建议用途

1. **轨迹分析**: 分析移动轨迹和模式
2. **时空分析**: 研究时间和空间的关联性
3. **GIS应用**: 导入地理信息系统进行可视化
4. **导航研究**: 用于导航算法验证和测试

## 注意事项

- 数据跨度较长(4年)，可能包含多次不同的采集
- 坐标主要集中在两个区域，可能是特定的测试或应用场景
- 建议在使用前进一步验证坐标的地理合理性
